<template>
  <div class="file-manager-page">
    <!-- 文件管理横幅 -->
    <div class="file-manager-banner">
      <div class="banner-background">
        <div class="file-pattern"></div>
      </div>
      <div class="banner-content">
        <div class="banner-text">
          <h1 class="banner-title">文件管理中心</h1>
          <p class="banner-subtitle">
            高效管理您的文件，支持批量操作、智能搜索和多种视图模式
          </p>
          <div class="banner-actions">
            <el-button type="primary" size="large" @click="showUploadDialog = true">
              <el-icon><Upload /></el-icon>
              上传文件
            </el-button>
            <el-button size="large" @click="createDirectory">
              <el-icon><FolderAdd /></el-icon>
              新建文件夹
            </el-button>
          </div>
        </div>
        <div class="banner-visual">
          <div class="file-manager-icon">
            <el-icon><FolderOpened /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <h2 class="section-title">
        <el-icon><DataAnalysis /></el-icon>
        文件统计
      </h2>
      <div class="stats-grid">
        <div class="stats-card primary">
          <div class="stats-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ files.length }}</div>
            <div class="stats-label">总文件数</div>
            <div class="stats-trend">
              <el-icon><TrendCharts /></el-icon>
              <span>点击查看详情</span>
            </div>
          </div>
        </div>

        <div class="stats-card success">
          <div class="stats-icon">
            <el-icon><Select /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ selectedFiles.length }}</div>
            <div class="stats-label">已选择</div>
            <div class="stats-trend">
              <el-icon><Check /></el-icon>
              <span>已选中文件</span>
            </div>
          </div>
        </div>

        <div class="stats-card warning">
          <div class="stats-icon">
            <el-icon><DataBoard /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ formatFileSize(totalSize) }}</div>
            <div class="stats-label">总大小</div>
            <div class="stats-trend">
              <el-icon><Monitor /></el-icon>
              <span>存储使用情况</span>
            </div>
          </div>
        </div>

        <div class="stats-card info">
          <div class="stats-icon">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ recentFilesCount }}</div>
            <div class="stats-label">最近文件</div>
            <div class="stats-trend">
              <el-icon><Calendar /></el-icon>
              <span>24小时内上传</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions-section">
      <h2 class="section-title">
        <el-icon><Lightning /></el-icon>
        快捷操作
      </h2>
      <div class="quick-actions-grid">
        <div class="quick-action-card" @click="showUploadDialog = true">
          <div class="action-icon">
            <el-icon><Upload /></el-icon>
          </div>
          <div class="action-content">
            <h3>上传文件</h3>
            <p>支持拖拽上传，多文件同时上传</p>
          </div>
          <div class="action-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="quick-action-card" @click="createDirectory">
          <div class="action-icon">
            <el-icon><FolderAdd /></el-icon>
          </div>
          <div class="action-content">
            <h3>新建文件夹</h3>
            <p>创建新的文件夹来组织您的文件</p>
          </div>
          <div class="action-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="quick-action-card" @click="viewMode = viewMode === 'table' ? 'grid' : 'table'">
          <div class="action-icon">
            <el-icon><Grid /></el-icon>
          </div>
          <div class="action-content">
            <h3>切换视图</h3>
            <p>在列表视图和网格视图之间切换</p>
          </div>
          <div class="action-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="quick-action-card" @click="$router.push('/batch-rename')" v-if="selectedFiles.length > 0">
          <div class="action-icon">
            <el-icon><Edit /></el-icon>
          </div>
          <div class="action-content">
            <h3>批量重命名</h3>
            <p>对选中的 {{ selectedFiles.length }} 个文件进行重命名</p>
          </div>
          <div class="action-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件浏览器 -->
    <div class="file-browser-section">
      <div class="section-header">
        <h2 class="section-title">
          <el-icon><FolderOpened /></el-icon>
          文件浏览器
        </h2>
        <div class="browser-controls">
          <div class="search-container">
            <el-input
              v-model="searchQuery"
              placeholder="搜索文件..."
              @input="handleSearch"
              style="width: 300px"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button @click="showBatchSearchDialog = true" type="primary">
              <el-icon><DocumentCopy /></el-icon>
              批量搜索
            </el-button>
          </div>
          <el-select v-model="filterType" placeholder="文件类型" style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="图片" value="jpg,jpeg,png,gif" />
            <el-option label="文档" value="pdf,doc,docx,txt" />
            <el-option label="视频" value="mp4,avi,mov" />
          </el-select>
          <el-button-group>
            <el-button 
              :type="viewMode === 'table' ? 'primary' : ''" 
              @click="viewMode = 'table'"
            >
              <el-icon><List /></el-icon>
            </el-button>
            <el-button 
              :type="viewMode === 'grid' ? 'primary' : ''" 
              @click="viewMode = 'grid'"
            >
              <el-icon><Grid /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>

      <div class="browser-content">
        <!-- 批量操作栏 -->
        <div v-if="selectedFiles.length > 0" class="batch-actions-bar">
          <div class="selected-info">
            <el-icon><Select /></el-icon>
            已选择 {{ selectedFiles.length }} 个文件
          </div>
          <div class="batch-actions">
            <el-button @click="$router.push('/batch-rename')" type="primary">
              <el-icon><Edit /></el-icon>
              批量重命名
            </el-button>
            <el-button @click="batchDelete" type="danger">
              <el-icon><Delete /></el-icon>
              批量删除
            </el-button>
            <el-button @click="selectedFiles = []">
              <el-icon><Close /></el-icon>
              取消选择
            </el-button>
          </div>
        </div>

        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'" class="table-container">
          <el-table
            :data="files"
            v-loading="loading"
            @selection-change="handleSelectionChange"
            empty-text="暂无文件数据"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="文件名" min-width="200">
              <template #default="{ row }">
                <div class="file-info">
                  <el-icon size="20">
                    <component :is="getFileIcon(getFileExtension(row.original_name))" />
                  </el-icon>
                  <span>{{ row.original_name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="size" label="大小" width="100">
              <template #default="{ row }">
                {{ formatFileSize(row.size) }}
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="上传时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button size="small" @click="downloadFile(row)">
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
                <el-button size="small" type="danger" @click="deleteFile(row)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 网格视图 -->
        <div v-else class="file-grid-container">
          <div v-if="files.length === 0 && !loading" class="empty-state">
            <div class="empty-illustration">
              <div class="empty-icon">
                <el-icon><DocumentRemove /></el-icon>
              </div>
              <div class="empty-content">
                <h3>还没有文件</h3>
                <p>开始上传您的第一个文件</p>
                <el-button type="primary" @click="showUploadDialog = true">
                  立即上传
                </el-button>
              </div>
            </div>
          </div>
          
          <div v-else class="file-grid" v-loading="loading">
            <div 
              v-for="file in files" 
              :key="file.id"
              class="file-item"
              :class="{ selected: selectedFiles.some(f => f.id === file.id) }"
              @click="toggleFileSelection(file)"
            >
              <div class="file-item-icon">
                <el-icon size="48">
                  <component :is="getFileIcon(getFileExtension(file.original_name))" />
                </el-icon>
              </div>
              <div class="file-item-name">{{ file.original_name }}</div>
              <div class="file-item-info">
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
              </div>
              <div class="file-item-actions">
                <el-button size="small" @click.stop="downloadFile(file)" type="primary">
                  <el-icon><Download /></el-icon>
                </el-button>
                <el-button size="small" @click.stop="deleteFile(file)" type="danger">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页和加载更多 -->
        <div v-if="total > 0" class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="smartPageSizes"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange"
            @current-change="handleCurrentPageChange"
            background
          />
          
          <!-- 加载更多按钮 -->
          <div v-if="hasMoreFiles" class="load-more-container">
            <el-button
              @click="loadMoreFiles"
              :loading="loadingMore"
              type="primary"
              size="large"
              class="load-more-btn"
            >
              <el-icon v-if="!loadingMore"><ArrowDown /></el-icon>
              {{ loadingMore ? '加载中...' : '加载更多文件' }}
            </el-button>
            <div class="load-more-info">
              已显示 {{ files.length }} / {{ total }} 个文件
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 重新设计的简洁上传对话框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="上传文件"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="!isUploading"
      :show-close="!isUploading"
      class="modern-upload-dialog"
    >
      <div class="upload-content">
        <!-- 拖拽上传区域 -->
        <div v-if="!isUploading && uploadFileList.length === 0" class="upload-zone">
          <el-upload
            ref="uploadRef"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            :on-progress="handleUploadProgress"
            :on-change="handleFileChange"
            name="files"
            multiple
            drag
            :auto-upload="false"
            class="clean-upload"
          >
            <div class="upload-area">
              <el-icon class="upload-icon"><UploadFilled /></el-icon>
              <div class="upload-text">
                <p class="main-text">拖拽文件到此处或点击上传</p>
                <p class="sub-text">支持多文件上传，单个文件不超过100MB</p>
              </div>
            </div>
          </el-upload>
        </div>

        <!-- 文件预览（选择阶段） -->
        <div v-else-if="!isUploading" class="file-preview">
          <div class="preview-header">
            <div class="file-summary">
              <span class="file-count">{{ uploadFileList.length }} 个文件</span>
              <span class="file-size">{{ formatTotalSize(uploadFileList) }}</span>
            </div>
            <el-button size="small" @click="clearFileList" text>清空</el-button>
          </div>
          
          <div class="file-list">
            <div v-for="(file, index) in uploadFileList.slice(0, 6)" :key="index" class="file-item">
              <el-icon class="file-icon"><Document /></el-icon>
              <div class="file-info">
                <span class="file-name">{{ truncateFileName(file.name, 25) }}</span>
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
              </div>
              <el-button size="small" @click="removeFile(index)" text :icon="Close" />
            </div>
            
            <div v-if="uploadFileList.length > 6" class="more-files">
              还有 {{ uploadFileList.length - 6 }} 个文件...
            </div>
          </div>
        </div>

        <!-- 上传进度（上传阶段） -->
        <div v-else class="upload-progress">
          <div class="progress-header">
            <h4>上传进度</h4>
            <div class="progress-stats">{{ completedFiles }}/{{ totalFiles }} 个文件</div>
          </div>
          
          <div class="main-progress">
            <el-progress
              :percentage="Math.round(overallProgress)"
              :status="uploadStatus"
              :stroke-width="8"
            />
            <div class="progress-info">
              <span>{{ Math.round(overallProgress) }}%</span>
              <span>{{ averageSpeed }}</span>
              <span>{{ estimatedTime }}</span>
            </div>
          </div>

          <div class="upload-stats">
            <div class="stat-item" v-if="completedFiles > 0">
              <el-icon class="success-icon"><Check /></el-icon>
              <span>已完成 {{ completedFiles }}</span>
            </div>
            <div class="stat-item" v-if="uploadingFiles > 0">
              <el-icon class="loading-icon"><Loading /></el-icon>
              <span>上传中 {{ uploadingFiles }}</span>
            </div>
            <div class="stat-item" v-if="errorFiles > 0">
              <el-icon class="error-icon"><Close /></el-icon>
              <span>失败 {{ errorFiles }}</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="!isUploading && uploadFileList.length === 0" @click="showUploadDialog = false">
            取消
          </el-button>
          <el-button v-if="isUploading" type="danger" @click="cancelUpload">
            取消上传
          </el-button>
          <el-button v-if="!isUploading && uploadFileList.length > 0" type="primary" @click="startUpload">
            开始上传
          </el-button>
          <el-button v-if="!isUploading && completedFiles > 0" type="success" @click="finishUpload">
            完成
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量搜索对话框 -->
    <el-dialog
      v-model="showBatchSearchDialog"
      title="批量搜索文件"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="batch-search-content">
        <div class="search-instructions">
          <h4>使用说明</h4>
          <ul>
            <li>每行输入一个搜索关键词</li>
            <li>支持粘贴多个关键词（自动按行分割）</li>
            <li>将搜索所有包含任意关键词的文件</li>
            <li>最多支持100个关键词</li>
          </ul>
        </div>
        
        <el-input
          v-model="batchSearchText"
          type="textarea"
          :rows="10"
          placeholder="请输入搜索关键词，每行一个&#10;例如：&#10;9200190392641700162321&#10;document.pdf&#10;image.jpg"
          @input="handleBatchSearchInput"
        />
        
        <div class="search-stats">
          <span>关键词数量: {{ batchSearchKeywords.length }}</span>
          <span v-if="batchSearchKeywords.length > 100" class="error-text">
            超出限制！最多支持100个关键词
          </span>
        </div>
        
        <div v-if="batchSearchKeywords.length > 0" class="keywords-preview">
          <h5>关键词预览:</h5>
          <div class="keywords-list">
            <el-tag
              v-for="(keyword, index) in batchSearchKeywords.slice(0, 10)"
              :key="index"
              size="small"
              class="keyword-tag"
            >
              {{ keyword }}
            </el-tag>
            <el-tag v-if="batchSearchKeywords.length > 10" size="small" type="info">
              +{{ batchSearchKeywords.length - 10 }} 更多...
            </el-tag>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBatchSearchDialog = false">取消</el-button>
          <el-button @click="clearBatchSearch">清空</el-button>
          <el-button
            type="primary"
            @click="executeBatchSearch"
            :disabled="batchSearchKeywords.length === 0 || batchSearchKeywords.length > 100"
          >
            搜索 ({{ batchSearchKeywords.length }} 个关键词)
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Upload,
  FolderAdd,
  Lightning,
  ArrowRight,
  ArrowDown,
  ArrowUp,
  Edit,
  Grid,
  FolderOpened,
  Search,
  List,
  Select,
  Close,
  Download,
  Delete,
  DocumentRemove,
  UploadFilled,
  InfoFilled,
  Warning,
  Check,
  Picture,
  Document,
  VideoPlay,
  DataAnalysis,
  TrendCharts,
  DataBoard,
  Monitor,
  Clock,
  Calendar,
  Loading,
  DocumentCopy,
  MoreFilled
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { apiMethods, formatFileSize, downloadFile as downloadFileUtil } from '@/utils/api'
import type { FileItem } from '@/types'

const authStore = useAuthStore()

const files = ref<FileItem[]>([])
const loading = ref(false)
const loadingMore = ref(false)
const searchQuery = ref('')
const filterType = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const selectedFiles = ref<FileItem[]>([])
const showUploadDialog = ref(false)
const viewMode = ref<'table' | 'grid'>('table')
const hasMoreFiles = ref(true)

// 批量搜索相关状态
const showBatchSearchDialog = ref(false)
const batchSearchText = ref('')
const batchSearchKeywords = ref<string[]>([])

// 上传进度相关状态
const isUploading = ref(false)
const uploadFileList = ref<any[]>([])
const currentUploadingFile = ref('')
const uploadRef = ref()

// 新增的上传统计变量
const totalUploadedBytes = ref(0)
const totalBytes = ref(0)
const overallAverageSpeed = ref(0)
const currentConcurrency = ref(0)
const maxConcurrency = ref(6)
const showQueueDetails = ref(false)

// 文件列表显示控制
const showAllFiles = ref(false)

// 拖拽状态
const isDragActive = ref(false)

// 文件预览视图模式
const fileViewMode = ref<'grid' | 'list'>('grid')

// 文件分页相关
const currentFilePage = ref(1)
const filePageSize = ref(5) // 减少每页显示数量

// 显示的文件列表（分页）
const displayedUploadFiles = computed(() => {
  const start = (currentFilePage.value - 1) * filePageSize.value
  const end = start + filePageSize.value
  return uploadFileList.value.slice(start, end).map((file, index) => ({
    ...file,
    originalIndex: start + index
  }))
})

// 检查是否有重复文件
const hasDuplicateFiles = computed(() =>
  uploadFileList.value.some(file => file.isDuplicate)
)

// 上传统计
const completedFiles = computed(() =>
  uploadFileList.value.filter(file => file.status === 'success').length
)
const totalFiles = computed(() => uploadFileList.value.length)
const overallProgress = computed(() => {
  if (totalFiles.value === 0) return 0
  // 基于已完成文件数量计算整体进度
  return (completedFiles.value / totalFiles.value) * 100
})
const uploadStatus = computed(() => {
  if (completedFiles.value === totalFiles.value && totalFiles.value > 0) return 'success'
  if (uploadFileList.value.some(file => file.status === 'error')) return 'exception'
  return undefined
})

// 新增的计算属性
const errorFiles = computed(() =>
  uploadFileList.value.filter(file => file.status === 'error').length
)

const uploadingFiles = computed(() =>
  uploadFileList.value.filter(file => file.status === 'uploading').length
)

const pendingFiles = computed(() =>
  uploadFileList.value.filter(file => file.status === 'ready' || !file.status).length
)

const displayedFiles = computed(() => {
  if (showAllFiles.value || uploadFileList.value.length <= 5) {
    return uploadFileList.value
  }
  return uploadFileList.value.slice(0, 5)
})

// 文件选择阶段显示的文件列表
const displayedSelectionFiles = computed(() => {
  if (showAllFiles.value || uploadFileList.value.length <= 3) {
    return uploadFileList.value
  }
  return uploadFileList.value.slice(0, 3)
})

// 计算总文件大小的方法
const formatTotalSize = (fileList: any[]) => {
  const totalBytes = fileList.reduce((sum, file) => sum + (file.size || 0), 0)
  return formatFileSize(totalBytes)
}

// 移除文件的方法
const removeFile = (index: number) => {
  uploadFileList.value.splice(index, 1)
  // 同时更新Element Plus的文件列表
  if (uploadRef.value) {
    const elementFileList = uploadRef.value.uploadFiles
    elementFileList.splice(index, 1)
  }
}

// 上传速度和时间估算
const averageSpeed = ref('0 KB/s')
const estimatedTime = ref('--')
const uploadStartTime = ref(0)
const uploadedBytes = ref(0)

const uploadUrl = computed(() => '/api/v1/files/upload')
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${authStore.token}`
}))

// 计算总文件大小
const totalSize = computed(() => {
  return files.value.reduce((total, file) => total + (file.size || 0), 0)
})

// 计算最近文件数量（24小时内）
const recentFilesCount = computed(() => {
  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
  return files.value.filter(file => {
    const fileDate = new Date(file.created_at)
    return fileDate > oneDayAgo
  }).length
})

// 智能分页大小计算
const smartPageSizes = computed(() => {
  const totalCount = total.value
  const baseSizes = [10, 20, 50]
  
  if (totalCount <= 50) {
    // 小数据量：提供基础选项
    return baseSizes
  } else if (totalCount <= 200) {
    // 中等数据量：添加100选项
    return [...baseSizes, 100]
  } else if (totalCount <= 500) {
    // 大数据量：添加200和500选项
    return [...baseSizes, 100, 200, 500]
  } else if (totalCount <= 1000) {
    // 超大数据量：添加更多选项，包括"全部"
    return [...baseSizes, 100, 200, 500, 1000, totalCount]
  } else if (totalCount <= 5000) {
    // 海量数据：提供更灵活的选项
    return [...baseSizes, 100, 200, 500, 1000, 2000, Math.ceil(totalCount / 2), totalCount]
  } else {
    // 超海量数据：提供分层选项，避免性能问题
    const quarter = Math.ceil(totalCount / 4)
    const half = Math.ceil(totalCount / 2)
    return [...baseSizes, 100, 200, 500, 1000, 2000, quarter, half, totalCount]
  }
})

const getFileExtension = (filename: string): string => {
  const lastDot = filename.lastIndexOf('.')
  return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : ''
}

const getFileIcon = (extension: string) => {
  const ext = extension?.toLowerCase() || ''
  if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(ext)) {
    return Picture
  } else if (['pdf', 'doc', 'docx', 'txt', 'md'].includes(ext)) {
    return Document
  } else if (['mp4', 'avi', 'mov', 'mkv', 'webm'].includes(ext)) {
    return VideoPlay
  } else if (['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(ext)) {
    return Document // 使用Document作为音频文件的图标
  } else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
    return Document
  } else if (['js', 'ts', 'vue', 'html', 'css', 'json', 'xml'].includes(ext)) {
    return Document
  }
  return Document
}

const loadFiles = async (append = false) => {
  try {
    if (append) {
      loadingMore.value = true
    } else {
      loading.value = true
      files.value = []
    }
    
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value
    }
    
    const response = await apiMethods.files.list(params)
    const newFiles = response.data.data.files
    
    if (append) {
      files.value = [...files.value, ...newFiles]
    } else {
      files.value = newFiles
    }
    
    total.value = response.data.data.pagination.total
    hasMoreFiles.value = files.value.length < total.value
  } catch (error) {
    console.error('加载文件列表失败:', error)
    ElMessage.error('加载文件列表失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

const loadMoreFiles = async () => {
  if (loadingMore.value || !hasMoreFiles.value) return
  currentPage.value++
  await loadFiles(true)
}

const handleSearch = () => {
  // 搜索时重置到第一页
  currentPage.value = 1
  loadFiles()
}

const handlePageSizeChange = (newPageSize: number) => {
  // 保存当前滚动位置
  const currentScrollY = window.scrollY
  
  pageSize.value = newPageSize
  currentPage.value = 1
  loadFiles().then(() => {
    // 数据加载完成后，恢复滚动位置
    nextTick(() => {
      window.scrollTo(0, currentScrollY)
    })
  })
}

const handleCurrentPageChange = (newPage: number) => {
  // 保存当前滚动位置
  const currentScrollY = window.scrollY
  
  currentPage.value = newPage
  loadFiles(false).then(() => {
    // 数据加载完成后，恢复滚动位置
    nextTick(() => {
      window.scrollTo(0, currentScrollY)
    })
  })
}

const handleSelectionChange = (selection: FileItem[]) => {
  selectedFiles.value = selection
}

const downloadFile = async (file: FileItem) => {
  try {
    const response = await apiMethods.files.download(file.id)
    downloadFileUtil(response.data, file.original_name)
    ElMessage.success('文件下载成功')
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  }
}

const deleteFile = async (file: FileItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${file.original_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await apiMethods.files.delete(file.id)
    ElMessage.success('文件删除成功')
    loadFiles()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文件失败:', error)
      ElMessage.error('删除文件失败')
    }
  }
}

const createDirectory = async () => {
  try {
    const { value: name } = await ElMessageBox.prompt(
      '请输入文件夹名称',
      '新建文件夹',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )
    
    if (name) {
      await apiMethods.directories.create({ name })
      ElMessage.success('文件夹创建成功')
      loadFiles()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('创建文件夹失败:', error)
      ElMessage.error('创建文件夹失败')
    }
  }
}

const beforeUpload = (file: File) => {
  const maxSize = 100 * 1024 * 1024 // 100MB
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过100MB')
    return false
  }
  return true
}


const formatDate = (date: string) => {
  return new Date(date).toLocaleString()
}

// 切换文件选择状态
const toggleFileSelection = (file: FileItem) => {
  const index = selectedFiles.value.findIndex(f => f.id === file.id)
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  } else {
    selectedFiles.value.push(file)
  }
}

// 批量删除文件
const batchDelete = async () => {
  if (selectedFiles.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？此操作不可撤销。`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 批量删除API调用
    const deletePromises = selectedFiles.value.map(file =>
      apiMethods.files.delete(file.id)
    )
    
    await Promise.all(deletePromises)
    ElMessage.success(`成功删除 ${selectedFiles.value.length} 个文件`)
    selectedFiles.value = []
    loadFiles()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 新的上传相关方法
const handleFileChange = (file: any, fileList: any[]) => {
  // 重置分页到第一页
  currentFilePage.value = 1
  
  // 当文件列表改变时更新我们的文件列表
  const newFileList = fileList.map((item, index) => ({
    name: item.name,
    size: item.size,
    status: 'ready',
    errorMessage: '',
    file: item.raw,
    progress: 0,
    uploadTime: null,
    id: Date.now() + Math.random() + index, // 添加唯一ID
    isDuplicate: false
  }))
  
  // 检查重复文件并标记
  const fileMap = new Map()
  const duplicateNames = new Set()
  
  newFileList.forEach(file => {
    const key = `${file.name}_${file.size}` // 使用文件名和大小作为唯一标识
    if (fileMap.has(key)) {
      duplicateNames.add(file.name)
    } else {
      fileMap.set(key, file)
    }
  })
  
  // 标记重复文件
  newFileList.forEach(file => {
    const key = `${file.name}_${file.size}`
    if (duplicateNames.has(file.name)) {
      file.isDuplicate = true
    }
  })
  
  uploadFileList.value = newFileList
  
  // 显示重复文件提示
  if (duplicateNames.size > 0) {
    ElMessage.warning({
      message: `检测到 ${duplicateNames.size} 个重复文件，已标记为重复`,
      duration: 3000,
      showClose: true
    })
  }
}

// 智能去重 - 移除重复文件
const removeDuplicateFiles = () => {
  const fileMap = new Map()
  const uniqueFiles: any[] = []
  
  uploadFileList.value.forEach(file => {
    const key = `${file.name}_${file.size}`
    if (!fileMap.has(key)) {
      fileMap.set(key, true)
      uniqueFiles.push({
        ...file,
        isDuplicate: false
      })
    }
  })
  
  const removedCount = uploadFileList.value.length - uniqueFiles.length
  uploadFileList.value = uniqueFiles
  
  if (removedCount > 0) {
    ElMessage.success(`已自动移除 ${removedCount} 个重复文件`)
  }
  
  // 重置分页
  currentFilePage.value = 1
}

// 智能重复文件处理
const handleDuplicateFile = (fileName: string) => {
  const timestamp = new Date().getTime()
  const extension = fileName.substring(fileName.lastIndexOf('.'))
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'))
  return `${nameWithoutExt}_${timestamp}${extension}`
}

const clearFileList = () => {
  uploadFileList.value = []
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

const startUpload = async () => {
  if (uploadFileList.value.length === 0) return
  
  // 预检查文件
  const validFiles = uploadFileList.value.filter(file => {
    if (file.size > 100 * 1024 * 1024) {
      file.status = 'error'
      file.errorMessage = '文件大小超过100MB限制'
      return false
    }
    return true
  })
  
  if (validFiles.length === 0) {
    ElMessage.error('没有有效的文件可以上传')
    return
  }
  
  isUploading.value = true
  uploadStartTime.value = Date.now()
  uploadedBytes.value = 0
  
  // 重置所有文件状态
  uploadFileList.value.forEach(file => {
    if (file.status !== 'error') {
      file.status = 'waiting'
      file.progress = 0
    }
  })
  
  try {
    // 智能并发控制：根据文件数量和大小动态调整并发数
    const totalFiles = uploadFileList.value.length
    const avgFileSize = uploadFileList.value.reduce((sum, file) => sum + file.size, 0) / totalFiles
    
    // 动态计算最优并发数
    let concurrentLimit = 3 // 默认值
    if (totalFiles > 100) {
      // 大量小文件：增加并发数
      concurrentLimit = avgFileSize < 1024 * 1024 ? 8 : 5 // 小于1MB用8个并发，否则5个
    } else if (totalFiles > 20) {
      concurrentLimit = avgFileSize < 5 * 1024 * 1024 ? 6 : 4 // 小于5MB用6个并发，否则4个
    }
    
    console.log(`[DEBUG] 智能并发控制: ${totalFiles}个文件, 平均大小${formatFileSize(avgFileSize)}, 并发数${concurrentLimit}`)
    
    // 使用信号量控制并发，不再按批次等待
    const semaphore = new Array(concurrentLimit).fill(null)
    let semaphoreIndex = 0
    
    const uploadPromises = uploadFileList.value
      .filter(file => file.status !== 'error')
      .map(async (file) => {
        // 等待获取信号量
        const currentIndex = semaphoreIndex % concurrentLimit
        semaphoreIndex++
        
        await new Promise(resolve => {
          const checkSemaphore = () => {
            if (semaphore[currentIndex] === null) {
              semaphore[currentIndex] = file
              resolve(null)
            } else {
              setTimeout(checkSemaphore, 10)
            }
          }
          checkSemaphore()
        })
        
        try {
          await uploadSingleFileWithRetry(file, 3)
        } finally {
          semaphore[currentIndex] = null
        }
      })
    
    // 等待所有上传完成
    await Promise.allSettled(uploadPromises)
    
    // 统计结果
    const successCount = uploadFileList.value.filter(f => f.status === 'success').length
    const errorCount = uploadFileList.value.filter(f => f.status === 'error').length
    
    if (successCount > 0) {
      ElMessage.success(`成功上传 ${successCount} 个文件${errorCount > 0 ? `，${errorCount} 个文件失败` : ''}`)
      loadFiles() // 刷新文件列表
    }
    
    if (errorCount === uploadFileList.value.length) {
      ElMessage.error('所有文件上传失败')
      // 为所有失败的文件记录日志
      uploadFileList.value.forEach(file => {
        if (file.status === 'error') {
          logClientError(file.name, file.errorMessage || '上传失败', 'upload_failed')
        }
      })
    }
    
  } catch (error) {
    console.error('上传过程中出错:', error)
    ElMessage.error('上传过程中出现错误')
  } finally {
    isUploading.value = false
    currentUploadingFile.value = ''
  }
}

// 带重试机制的文件上传
const uploadSingleFileWithRetry = async (fileItem: any, maxRetries: number): Promise<void> => {
  let retries = 0
  
  while (retries < maxRetries) {
    try {
      await uploadSingleFile(fileItem, 0)
      if (fileItem.status === 'success') {
        return
      }
    } catch (error) {
      console.error(`文件 ${fileItem.name} 上传失败，重试 ${retries + 1}/${maxRetries}:`, error)
    }
    
    retries++
    if (retries < maxRetries) {
      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * retries))
      fileItem.status = 'uploading'
      fileItem.errorMessage = `重试中 (${retries}/${maxRetries})`
    }
  }
  
  // 所有重试都失败了
  fileItem.status = 'error'
  fileItem.errorMessage = `上传失败，已重试 ${maxRetries} 次`
}

const uploadSingleFile = (fileItem: any, index: number): Promise<void> => {
  return new Promise((resolve, reject) => {
    const formData = new FormData()
    formData.append('files', fileItem.file)
    
    const xhr = new XMLHttpRequest()
    fileItem.xhr = xhr // 保存xhr引用以便取消
    fileItem.status = 'uploading'
    fileItem.uploadTime = Date.now()
    currentUploadingFile.value = fileItem.name
    
    // 上传进度监听 - 增强实时性
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        const newProgress = Math.round((event.loaded / event.total) * 100)
        fileItem.progress = newProgress
        fileItem.uploadedBytes = event.loaded
        fileItem.totalBytes = event.total
        
        // 计算当前文件的上传速度
        const currentTime = Date.now()
        if (!fileItem.lastProgressTime) {
          fileItem.lastProgressTime = fileItem.uploadTime
          fileItem.lastProgressBytes = 0
        }
        
        const timeDiff = (currentTime - fileItem.lastProgressTime) / 1000
        const bytesDiff = event.loaded - fileItem.lastProgressBytes
        
        if (timeDiff > 0.5) { // 每0.5秒更新一次速度
          fileItem.currentSpeed = bytesDiff / timeDiff
          fileItem.lastProgressTime = currentTime
          fileItem.lastProgressBytes = event.loaded
        }
        
        // 实时更新总体进度
        updateOverallProgress()
      }
    })
    
    // 上传完成监听
    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        try {
          const response = JSON.parse(xhr.responseText)
          if (response.success) {
            fileItem.status = 'success'
            fileItem.progress = 100
            fileItem.uploadTime = Date.now() - fileItem.uploadTime
            
            // 移除单个文件成功提示，避免消息轰炸
          } else {
            fileItem.status = 'error'
            fileItem.errorMessage = response.message || '上传失败'
            fileItem.progress = 0
            // 记录服务器返回的失败日志
            logClientError(fileItem.name, response.message || '上传失败', 'server_error')
          }
        } catch (error) {
          fileItem.status = 'error'
          fileItem.errorMessage = '响应解析失败'
          fileItem.progress = 0
          // 记录响应解析失败日志
          logClientError(fileItem.name, '响应解析失败', 'parse_error')
        }
      } else {
        fileItem.status = 'error'
        fileItem.errorMessage = `HTTP ${xhr.status}: ${xhr.statusText}`
        fileItem.progress = 0
        // 记录HTTP错误日志
        logClientError(fileItem.name, `HTTP ${xhr.status}: ${xhr.statusText}`, 'http_error')
      }
      resolve()
    })
    
    // 上传错误监听
    xhr.addEventListener('error', () => {
      fileItem.status = 'error'
      fileItem.errorMessage = '网络连接错误'
      fileItem.progress = 0
      // 记录客户端网络错误日志
      logClientError(fileItem.name, '网络连接错误', 'network_error')
      resolve()
    })
    
    // 上传超时监听
    xhr.addEventListener('timeout', () => {
      fileItem.status = 'error'
      fileItem.errorMessage = '上传超时'
      fileItem.progress = 0
      // 记录客户端超时错误日志
      logClientError(fileItem.name, '上传超时', 'timeout_error')
      resolve()
    })
    
    // 上传中断监听
    xhr.addEventListener('abort', () => {
      fileItem.status = 'error'
      fileItem.errorMessage = '上传已取消'
      fileItem.progress = 0
      // 记录客户端中断错误日志
      logClientError(fileItem.name, '上传已取消', 'abort_error')
      resolve()
    })
    
    // 设置超时时间（5分钟）
    xhr.timeout = 5 * 60 * 1000
    
    // 发送请求
    xhr.open('POST', uploadUrl.value)
    xhr.setRequestHeader('Authorization', uploadHeaders.value.Authorization)
    xhr.send(formData)
  })
}

const updateOverallProgress = () => {
  const elapsed = (Date.now() - uploadStartTime.value) / 1000
  if (elapsed <= 0) return
  
  // 计算总字节数和已传输字节数
  const totalBytes = uploadFileList.value.reduce((sum, file) => sum + file.size, 0)
  let transferredBytes = 0
  
  uploadFileList.value.forEach(file => {
    if (file.status === 'success') {
      transferredBytes += file.size
    } else if (file.status === 'uploading' && file.progress > 0) {
      transferredBytes += (file.size * file.progress / 100)
    }
  })
  
  // 计算实时上传速度
  const currentSpeed = transferredBytes / elapsed
  averageSpeed.value = formatSpeed(currentSpeed)
  
  // 计算剩余时间（基于当前速度和剩余字节数）
  const remainingBytes = totalBytes - transferredBytes
  if (currentSpeed > 0 && remainingBytes > 0) {
    const estimatedRemainingTime = remainingBytes / currentSpeed
    estimatedTime.value = formatTime(estimatedRemainingTime)
  } else if (remainingBytes <= 0) {
    estimatedTime.value = '完成'
  } else {
    estimatedTime.value = '计算中...'
  }
  
  // 更新总体进度百分比
  const overallProgressPercent = totalBytes > 0 ? Math.round((transferredBytes / totalBytes) * 100) : 0
  
  // 调试信息
  if (elapsed % 2 < 0.1) { // 每2秒输出一次调试信息
    console.log(`[DEBUG] 进度更新: ${overallProgressPercent}%, 速度: ${averageSpeed.value}, 剩余时间: ${estimatedTime.value}`)
    console.log(`[DEBUG] 已传输: ${formatFileSize(transferredBytes)} / ${formatFileSize(totalBytes)}`)
  }
}

const formatSpeed = (bytesPerSecond: number): string => {
  if (bytesPerSecond < 1024) return `${Math.round(bytesPerSecond)} B/s`
  if (bytesPerSecond < 1024 * 1024) return `${Math.round(bytesPerSecond / 1024)} KB/s`
  return `${Math.round(bytesPerSecond / (1024 * 1024))} MB/s`
}

const formatTime = (seconds: number): string => {
  if (seconds < 60) return `${Math.round(seconds)}秒`
  if (seconds < 3600) return `${Math.round(seconds / 60)}分钟`
  return `${Math.round(seconds / 3600)}小时`
}

const cancelUpload = () => {
  isUploading.value = false
  currentUploadingFile.value = ''
  
  // 取消所有正在进行的上传
  uploadFileList.value.forEach(file => {
    if (file.status === 'uploading' && file.xhr) {
      file.xhr.abort()
    }
    if (file.status === 'uploading' || file.status === 'waiting') {
      file.status = 'error'
      file.errorMessage = '用户取消'
      file.progress = 0
    }
  })
  
  ElMessage.info('上传已取消')
}

// 取消单个文件上传
const cancelSingleUpload = (fileItem: any) => {
  if (fileItem.xhr && fileItem.status === 'uploading') {
    fileItem.xhr.abort()
    fileItem.status = 'error'
    fileItem.errorMessage = '用户取消'
    fileItem.progress = 0
    ElMessage.info(`已取消 ${fileItem.name} 的上传`)
  }
}

// 重试单个文件上传
const retrySingleUpload = async (fileItem: any) => {
  if (fileItem.status === 'error') {
    fileItem.status = 'ready'
    fileItem.errorMessage = ''
    fileItem.progress = 0
    
    try {
      await uploadSingleFile(fileItem, 0)
    } catch (error) {
      console.error('重试上传失败:', error)
    }
  }
}

const finishUpload = () => {
  showUploadDialog.value = false
  uploadFileList.value = []
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 重写原有的上传处理方法
const handleUploadSuccess = (response: any, file: any, fileList: any[]) => {
  // 这个方法现在由我们的自定义上传逻辑处理，这里保留为空
}

const handleUploadError = (error: any, file: any, fileList: any[]) => {
  // 这个方法现在由我们的自定义上传逻辑处理，这里保留为空
}

// 上传进度处理
const handleUploadProgress = (event: any, file: any, fileList: any[]) => {
  // 这个方法现在由我们的自定义上传逻辑处理，这里保留为空
}

// 批量搜索相关方法
const handleBatchSearchInput = () => {
  // 处理批量搜索输入，按行分割关键词
  const keywords = batchSearchText.value
    .split('\n')
    .map(keyword => keyword.trim())
    .filter(keyword => keyword.length > 0)
  
  batchSearchKeywords.value = keywords
}

const clearBatchSearch = () => {
  batchSearchText.value = ''
  batchSearchKeywords.value = []
}

const executeBatchSearch = async () => {
  if (batchSearchKeywords.value.length === 0) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  
  if (batchSearchKeywords.value.length > 100) {
    ElMessage.error('关键词数量不能超过100个')
    return
  }
  
  try {
    loading.value = true
    showBatchSearchDialog.value = false
    
    // 修改后端API调用，支持批量搜索
    const response = await apiMethods.files.batchSearch({
      keywords: batchSearchKeywords.value,
      page: 1,
      limit: pageSize.value
    })
    
    files.value = response.data.data.files
    total.value = response.data.data.pagination.total
    currentPage.value = 1
    hasMoreFiles.value = files.value.length < total.value
    
    // 将批量搜索关键词合并为单个搜索查询以便显示
    searchQuery.value = `批量搜索(${batchSearchKeywords.value.length}个关键词)`
    
    ElMessage.success(`找到 ${files.value.length} 个匹配的文件`)
    
  } catch (error) {
    console.error('批量搜索失败:', error)
    ElMessage.error('批量搜索失败，请重试')
  } finally {
    loading.value = false
  }
}

// 拖拽处理方法
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  isDragActive.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  isDragActive.value = false
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragActive.value = false
}

// 文件预览相关方法
const isImageFile = (file: any): boolean => {
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp']
  const extension = getFileExtension(file.name)
  return imageTypes.includes(extension.toLowerCase())
}

const hasFilePreview = (file: any): boolean => {
  return isImageFile(file)
}

const getFilePreviewUrl = (file: any): string => {
  if (file.raw && isImageFile(file)) {
    return URL.createObjectURL(file.raw)
  }
  return ''
}

const handlePreviewError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

const getFileTypeClass = (file: any): string => {
  const extension = getFileExtension(file.name).toLowerCase()
  if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension)) {
    return 'image-type'
  } else if (['pdf', 'doc', 'docx'].includes(extension)) {
    return 'document-type'
  } else if (['mp4', 'avi', 'mov', 'mkv'].includes(extension)) {
    return 'video-type'
  } else if (['mp3', 'wav', 'flac'].includes(extension)) {
    return 'audio-type'
  }
  return 'default-type'
}

const getFileTypeDescription = (file: any): string => {
  const extension = getFileExtension(file.name).toLowerCase()
  const typeMap: Record<string, string> = {
    'jpg': '图片文件',
    'jpeg': '图片文件',
    'png': '图片文件',
    'gif': '动图文件',
    'webp': '图片文件',
    'svg': '矢量图',
    'pdf': 'PDF文档',
    'doc': 'Word文档',
    'docx': 'Word文档',
    'txt': '文本文件',
    'mp4': '视频文件',
    'avi': '视频文件',
    'mov': '视频文件',
    'mp3': '音频文件',
    'wav': '音频文件',
    'zip': '压缩文件',
    'rar': '压缩文件'
  }
  return typeMap[extension] || '未知类型'
}

// 记录客户端错误日志
const logClientError = async (fileName: string, errorMessage: string, errorType: string) => {
  console.log(`[DEBUG] 尝试记录失败日志: ${fileName}, 错误: ${errorMessage}, 类型: ${errorType}`)
  
  try {
    // 尝试调用后端API记录日志，如果失败则记录到本地存储
    const response = await apiMethods.logs.create({
      action: 'upload',
      resource: fileName,
      old_value: '',
      new_value: '',
      status: 'failed',
      error_msg: `${errorType}: ${errorMessage}`
    })
    console.log(`[DEBUG] 服务器日志记录成功:`, response.data)
  } catch (error) {
    // 如果后端API调用失败，记录到本地存储
    console.error('[DEBUG] 无法记录服务器日志，记录到本地存储:', error)
    const clientLogs = JSON.parse(localStorage.getItem('clientErrorLogs') || '[]')
    const logEntry = {
      timestamp: new Date().toISOString(),
      action: 'upload',
      resource: fileName,
      status: 'failed',
      error_msg: `${errorType}: ${errorMessage}`,
      type: 'client_error'
    }
    clientLogs.push(logEntry)
    // 只保留最近100条客户端日志
    if (clientLogs.length > 100) {
      clientLogs.splice(0, clientLogs.length - 100)
    }
    localStorage.setItem('clientErrorLogs', JSON.stringify(clientLogs))
    console.log(`[DEBUG] 本地日志记录成功:`, logEntry)
  }
}

// 队列管理方法
const toggleQueueDetails = () => {
  showQueueDetails.value = !showQueueDetails.value
}

const getUploadingFiles = () => {
  return uploadFileList.value.filter(file => file.status === 'uploading')
}

const getPendingFiles = () => {
  return uploadFileList.value.filter(file => file.status === 'ready' || !file.status)
}

// 格式化速度显示

// 截断文件名显示
const truncateFileName = (fileName: string, maxLength: number): string => {
  if (fileName.length <= maxLength) return fileName
  
  const extension = fileName.split('.').pop() || ''
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'))
  const maxNameLength = maxLength - extension.length - 4 // 4 for "..." and "."
  
  if (maxNameLength <= 0) return fileName.substring(0, maxLength - 3) + '...'
  
  return nameWithoutExt.substring(0, maxNameLength) + '...' + '.' + extension
}

onMounted(() => {
  loadFiles()
  
  // 强制设置标题样式 - 使用多种选择器确保找到元素
  nextTick(() => {
    const selectors = [
      '.file-manager-banner .banner-title',
      '.file-manager-page .banner-title',
      '.file-manager-page h1',
      '.file-manager-banner h1',
      '.title-content h1',
      'h1.banner-title',
      'h1'
    ]
    
    let titleElement: HTMLElement | null = null
    
    // 尝试多个选择器找到标题元素
    for (const selector of selectors) {
      titleElement = document.querySelector(selector) as HTMLElement
      if (titleElement && titleElement.textContent?.includes('文件管理中心')) {
        break
      }
    }
    
    if (titleElement) {
      // 强制设置所有可能影响字体大小的CSS属性
      const styles = {
        'font-size': '1.25rem',
        'font-weight': '600',
        'line-height': '1.2',
        'margin': '0',
        'margin-top': '0',
        'margin-bottom': '0',
        'margin-left': '0',
        'margin-right': '0',
        'padding': '0',
        'padding-top': '0',
        'padding-bottom': '0',
        'padding-left': '0',
        'padding-right': '0',
        'letter-spacing': '-0.025em',
        'color': '#1e293b',
        'border': 'none',
        'background': 'none',
        'text-decoration': 'none',
        'outline': 'none',
        'box-shadow': 'none',
        'transform': 'none',
        'zoom': '1',
        'scale': '1'
      }
      
      // 设置每个样式属性
      Object.entries(styles).forEach(([property, value]) => {
        titleElement!.style.setProperty(property, value, 'important')
      })
      
      // 确保类名正确
      titleElement.className = 'banner-title'
      
      console.log('文件管理中心标题样式已强制设置:', titleElement.style.fontSize)
    } else {
      console.warn('未找到文件管理中心标题元素')
    }
  })
  
  // 延迟再次检查，确保样式生效
  setTimeout(() => {
    const titleElement = document.querySelector('.file-manager-banner .banner-title') as HTMLElement
    if (titleElement) {
      titleElement.style.setProperty('font-size', '1.25rem', 'important')
      console.log('延迟检查 - 文件管理中心标题样式:', titleElement.style.fontSize)
    }
  }, 100)
})
</script>

<style scoped>
.file-manager-page {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
  background: #fafbfc;
  min-height: 100vh;
}

/* 文件管理横幅 - 简约现代风格 */
.file-manager-banner {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  color: #334155;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.banner-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.03;
}

.file-pattern {
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, #64748b 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, #64748b 1px, transparent 1px);
  background-size: 80px 80px, 120px 120px;
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.banner-text {
  flex: 1;
}

.file-manager-banner .banner-title,
.file-manager-page .banner-title,
h1.banner-title,
.file-manager-banner h1,
.file-manager-page h1,
.banner-title {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  margin: 0 0 8px 0 !important;
  color: #1e293b !important;
  letter-spacing: -0.025em !important;
  line-height: 1.2 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  text-decoration: none !important;
  outline: none !important;
  box-shadow: none !important;
  transform: none !important;
  zoom: 1 !important;
  scale: 1 !important;
}

.banner-subtitle {
  font-size: 0.95rem;
  margin: 0 0 16px 0;
  color: #64748b;
  line-height: 1.4;
  font-weight: 400;
}

.banner-actions {
  display: flex;
  gap: 12px;
}

.banner-visual {
  position: relative;
  width: 80px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-manager-icon {
  width: 48px;
  height: 48px;
  background: #3b82f6;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 3px 8px rgba(59, 130, 246, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.file-manager-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);
}

/* 仅在支持hover的设备上启用脉冲动画 */
@media (hover: hover) and (prefers-reduced-motion: no-preference) {
  .file-manager-banner:hover .file-manager-icon {
    animation: iconPulse 3s ease-in-out infinite;
  }
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  }
}

/* 区块标题 - 简约现代风格 */
.section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 28px 0;
  display: flex;
  align-items: center;
  gap: 10px;
  letter-spacing: -0.025em;
}

.section-title .el-icon {
  color: #3b82f6;
  font-size: 1.2rem;
}

/* 统计卡片 - 紧凑现代风格 */
.stats-section {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 10px;
  max-width: 100%;
}

.stats-card {
  background: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  padding: 8px 10px !important;
  position: relative !important;
  overflow: visible !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  min-height: 60px !important;
  border-left: 3px solid transparent !important;
}

.stats-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #e5e7eb;
}

.stats-card.primary {
  border-left-color: #3b82f6;
}

.stats-card.success {
  border-left-color: #10b981;
}

.stats-card.warning {
  border-left-color: #f59e0b;
}

.stats-card.info {
  border-left-color: #06b6d4;
}

.stats-card.primary:hover {
  border-left-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.stats-card.success:hover {
  border-left-color: #10b981;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.stats-card.warning:hover {
  border-left-color: #f59e0b;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

.stats-card.info:hover {
  border-left-color: #06b6d4;
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.15);
}


.stats-icon {
  width: 36px !important;
  height: 36px !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1rem !important;
  position: relative !important;
  top: auto !important;
  right: auto !important;
  z-index: 1 !important;
  color: white !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
  flex-shrink: 0 !important;
  transition: all 0.3s ease !important;
  overflow: visible !important;
}

.stats-card.primary .stats-icon {
  background: #3b82f6;
}

.stats-card.success .stats-icon {
  background: #10b981;
}

.stats-card.warning .stats-icon {
  background: #f59e0b;
}

.stats-card.info .stats-icon {
  background: #06b6d4;
}

.stats-card:hover .stats-icon {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.stats-icon .el-icon {
  font-size: 1rem !important;
  line-height: 1 !important;
  width: 1rem !important;
  height: 1rem !important;
  display: inline-block !important;
}

.stats-content {
  flex: 1;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1px;
}

.stats-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0;
  line-height: 1.1;
}

.stats-label {
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
  margin-bottom: 1px;
}

.stats-trend {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 0.625rem;
  color: #9ca3af;
  font-weight: 400;
  opacity: 0.8;
}

.stats-trend .el-icon {
  font-size: 0.625rem;
}

/* 快捷操作 - 现代化卡片设计 */
.quick-actions-section {
  margin-bottom: 32px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.quick-action-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  min-height: 80px;
  position: relative;
  overflow: hidden;
}

.quick-action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 0;
}

.quick-action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-color: rgba(59, 130, 246, 0.3);
}

.quick-action-card:hover::before {
  opacity: 1;
}

.action-icon {
  width: 36px !important;
  height: 36px !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1rem !important;
  position: relative !important;
  top: auto !important;
  right: auto !important;
  z-index: 1 !important;
  color: white !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
  flex-shrink: 0 !important;
  transition: all 0.3s ease !important;
  overflow: visible !important;
}

.quick-action-card:hover .action-icon {
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.action-icon .el-icon {
  font-size: 1rem !important;
  line-height: 1 !important;
  width: 1rem !important;
  height: 1rem !important;
  display: inline-block !important;
}

.action-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: relative;
  z-index: 1;
}

.action-content h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.action-content p {
  margin: 0;
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.4;
  opacity: 0.9;
}

.action-arrow {
  color: #94a3b8;
  transition: all 0.4s ease;
  font-size: 1.125rem;
  position: relative;
  z-index: 1;
}

.quick-action-card:hover .action-arrow {
  color: #3b82f6;
  transform: translateX(4px);
}

/* 特定卡片的图标颜色 - 与统计卡片保持一致 */
.quick-action-card:nth-child(1) .action-icon {
  background: #3b82f6;
}

.quick-action-card:nth-child(2) .action-icon {
  background: #10b981;
}

.quick-action-card:nth-child(3) .action-icon {
  background: #f59e0b;
}

.quick-action-card:nth-child(4) .action-icon {
  background: #06b6d4;
}

.quick-action-card:hover .action-icon {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

/* 文件浏览器 - 简约现代风格 */
.file-browser-section {
  margin-bottom: 48px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 28px;
}

.section-header h2 {
  margin: 0;
}

.browser-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.browser-content {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 批量操作栏 - 简约现代风格 */
.batch-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 28px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.selected-info {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: #1e293b;
  font-size: 0.95rem;
}

.batch-actions {
  display: flex;
  gap: 12px;
}

/* 表格容器 - 简约现代风格 */
.table-container {
  padding: 28px;
  max-height: 600px;
  overflow-y: auto;
  /* 添加硬件加速优化 */
  transform: translateZ(0);
  will-change: scroll-position;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  text-align: left; /* 确保文件名左对齐 */
}

.file-info span {
  text-align: left; /* 确保文件名文本左对齐 */
  flex: 1;
  min-width: 0;
  word-break: break-word;
}

/* 网格视图 - 简约现代风格 */
.file-grid-container {
  padding: 28px;
  max-height: 600px;
  overflow-y: auto;
  /* 添加硬件加速优化 */
  transform: translateZ(0);
  will-change: scroll-position;
}

.empty-state {
  padding: 48px 24px;
  text-align: center;
}

.empty-illustration {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-icon {
  width: 64px;
  height: 64px;
  background: var(--bg-tertiary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--text-tertiary);
}

.empty-content h3 {
  margin: 0 0 8px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.empty-content p {
  margin: 0 0 24px 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 20px;
}

.file-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.file-item:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.file-item.selected {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.04);
}

.file-item-icon {
  margin-bottom: 16px;
}

.file-item-name {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 12px;
  word-break: break-word;
  line-height: 1.4;
  text-align: left;
  font-size: 0.9rem;
}

.file-item-info {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  font-size: 0.875rem;
  gap: 8px;
}

.file-size {
  font-weight: 500;
  color: #64748b;
}

.file-item-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

/* 分页 - 简约现代风格 */
.pagination-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 28px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  gap: 24px;
}

/* 加载更多 */
.load-more-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.load-more-btn {
  min-width: 200px;
  border-radius: 25px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.load-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.load-more-info {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-align: center;
}

/* 上传对话框 */
.upload-dialog-content {
  padding: 16px;
}

.upload-area {
  border-radius: 12px;
  overflow: hidden;
}

.modern-upload {
  border-radius: 12px;
}

.upload-content {
  padding: 48px;
  text-align: center;
}

.upload-icon {
  font-size: 4rem;
  color: var(--primary);
  margin-bottom: 24px;
}

.upload-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.upload-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
}

.upload-tips {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-manager-page {
    padding: 16px;
  }
  
  .welcome-banner {
    padding: 24px;
  }
  
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .banner-title {
    font-size: 1.25rem !important;
  }
  
  .welcome-actions {
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .stats-card {
    padding: 10px;
    gap: 8px;
    min-height: 55px;
  }

  .stats-icon {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }

  .stats-icon .el-icon {
    font-size: 0.9rem !important;
    width: 0.9rem !important;
    height: 0.9rem !important;
  }

  .stats-value {
    font-size: 1.3rem;
  }

  .stats-label {
    font-size: 0.75rem;
  }

  .stats-trend {
    font-size: 0.65rem;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .quick-action-card {
    padding: 16px;
    gap: 12px;
    min-height: 70px;
  }

  .action-icon {
    width: 40px;
    height: 40px;
    font-size: 1.125rem;
    border-radius: 10px;
  }

  .action-content h3 {
    font-size: 0.9rem;
  }

  .action-content p {
    font-size: 0.8rem;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .browser-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .file-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .banner-title {
    font-size: 1.25rem !important;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .stats-card {
    padding: 8px;
    gap: 6px;
    min-height: 50px;
  }

  .stats-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }

  .stats-icon .el-icon {
    font-size: 0.8rem !important;
    width: 0.8rem !important;
    height: 0.8rem !important;
  }

  .stats-value {
    font-size: 1.1rem;
  }

  .stats-label {
    font-size: 0.7rem;
  }

  .stats-trend {
    font-size: 0.6rem;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .quick-action-card {
    padding: 14px;
    gap: 10px;
    min-height: 60px;
  }

  .action-icon {
    width: 36px;
    height: 36px;
    font-size: 1rem;
    border-radius: 8px;
  }

  .action-content h3 {
    font-size: 0.85rem;
  }

  .action-content p {
    font-size: 0.75rem;
  }
   
  .welcome-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .welcome-actions .el-button {
    width: 100%;
  }
  
  .stats-card {
    padding: 16px;
  }
  
  .quick-action-card {
    padding: 8px;
    gap: 8px;
    min-height: 50px;
  }

  .action-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }

  .action-content h3 {
    font-size: 0.8rem;
  }

  .action-content p {
    font-size: 0.65rem;
  }
  
  .file-grid {
    grid-template-columns: 1fr;
  }
}

/* 上传进度样式 */
.upload-progress-section {
  margin-top: 24px;
  padding: 20px;
  background: var(--bg-tertiary);
  border-radius: 12px;
  border: 1px solid var(--border-primary);
}

.progress-header {
  margin-bottom: 20px;
}

.progress-header h4 {
  margin: 0 0 16px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.overall-progress {
  background: var(--bg-card);
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--border-primary);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.file-progress-list {
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 16px;
  /* 添加硬件加速优化 */
  transform: translateZ(0);
  will-change: scroll-position;
}

.file-progress-list.collapsed {
  max-height: 200px;
}

/* 文件列表控制区域 */
.file-list-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: var(--bg-card);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
}

.file-stats {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-item.success {
  color: #22c55e;
}

.stat-item.error {
  color: #ef4444;
}

.stat-item.uploading {
  color: var(--primary);
}

.list-toggle {
  flex-shrink: 0;
}

/* 折叠提示 */
.collapsed-hint {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  margin-top: 8px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px dashed var(--border-primary);
}

.hint-content {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.hint-content .el-icon {
  color: var(--text-tertiary);
}

.file-progress-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: var(--bg-card);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
  transition: all 0.3s ease;
}

.file-progress-item.uploading {
  border-color: var(--primary);
  background: rgba(99, 102, 241, 0.05);
}

.file-progress-item.success {
  border-color: #22c55e;
  background: rgba(34, 197, 94, 0.05);
}

.file-progress-item.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

.file-progress-item .file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.file-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-size: 1rem;
}

.file-progress-item.success .file-icon {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.file-progress-item.error .file-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.file-progress-item.uploading .file-icon {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-progress-item .file-size {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.file-status {
  flex: 0 0 120px;
  margin-left: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.status-text {
  font-size: 0.875rem;
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 12px;
  text-align: center;
}

.status-text.success {
  color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
}

.status-text.uploading {
  color: var(--primary);
  background: rgba(99, 102, 241, 0.1);
}

.error-message {
  font-size: 0.75rem;
  color: #ef4444;
  margin-top: 4px;
  text-align: right;
}

.current-status {
  padding: 16px;
  background: var(--bg-card);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
}

.status-text {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.upload-stats {
  display: flex;
  gap: 24px;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
}

/* 统一的头部样式 */
.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.progress-header h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 文件操作区域样式 */
.file-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 200px;
}

.file-progress-item.ready {
  border-color: var(--border-primary);
  background: var(--bg-card);
}

.file-progress-item.ready:hover {
  border-color: var(--primary);
  background: rgba(99, 102, 241, 0.05);
}

.file-progress-item.ready .file-icon {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary);
}

/* 批量搜索对话框样式 */
.batch-search-content {
  padding: 16px;
}

.search-instructions {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.search-instructions h4 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.search-instructions ul {
  margin: 0;
  padding-left: 20px;
  color: var(--text-secondary);
}

.search-instructions li {
  margin-bottom: 4px;
  line-height: 1.4;
}

.search-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  padding: 8px 12px;
  background: var(--bg-tertiary);
  border-radius: 6px;
  font-size: 0.875rem;
}

.error-text {
  color: #ef4444;
  font-weight: 500;
}

.keywords-preview {
  margin-top: 16px;
  padding: 16px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
}

.keywords-preview h5 {
  margin: 0 0 12px 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.keyword-tag {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search-container {
  display: flex;
  gap: 12px;
  align-items: center;
}
/* 上传中视图样式 */
.uploading-view {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.overall-progress {
  background: white;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-text {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

.files-count {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.main-progress {
  margin-top: 8px;
}

.current-status {
  background: white;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.current-file {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 500;
  color: #1e293b;
}

.upload-stats {
  display: flex;
  gap: 24px;
  font-size: 0.875rem;
  color: #64748b;
}

.upload-summary {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.summary-item.success {
  color: #22c55e;
  border-color: rgba(34, 197, 94, 0.2);
  background: rgba(34, 197, 94, 0.05);
}

.summary-item.error {
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.2);
  background: rgba(239, 68, 68, 0.05);
}

.summary-item.uploading {
  color: #3b82f6;
  border-color: rgba(59, 130, 246, 0.2);
  background: rgba(59, 130, 246, 0.05);
}

.file-selection-view {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .uploading-view {
    gap: 16px;
  }
  
  .overall-progress {
    padding: 20px;
  }
  
  .progress-text {
    font-size: 1rem;
  }
  
  .current-status {
    padding: 16px;
  }
  
  .upload-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .upload-summary {
    gap: 12px;
  }
  
  .summary-item {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .overall-progress {
    padding: 16px;
  }
  
  .progress-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .progress-text {
    font-size: 0.95rem;
  }
  
  .files-count {
    font-size: 0.8rem;
  }
  
  .current-file {
    font-size: 0.9rem;
  }
  
  .upload-summary {
    flex-direction: column;
    gap: 8px;
  }
  
  .summary-item {
    justify-content: center;
  }
}

/* 紧凑型上传对话框样式 */
.compact-upload-dialog {
  border-radius: 12px;
}

.upload-dialog-content {
  padding: 24px;
}

.upload-area {
  border-radius: 12px;
  overflow: hidden;
}

/* 紧凑型拖拽上传区域 */
.modern-upload-zone {
  border-radius: 12px;
  border: 2px dashed #d1d5db;
  background: #fafbfc;
  transition: all 0.3s ease;
  position: relative;
}

.modern-upload-zone:hover {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.upload-zone-content {
  padding: 40px 24px;
  text-align: center;
  transition: all 0.3s ease;
}

.upload-zone-content.drag-active {
  background: rgba(59, 130, 246, 0.05);
  border-radius: 12px;
}

.upload-icon {
  font-size: 3rem;
  color: #3b82f6;
  margin-bottom: 16px;
}

.upload-text {
  max-width: 300px;
  margin: 0 auto;
}

.upload-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.upload-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.5;
}

.upload-link {
  color: #3b82f6;
  font-weight: 500;
  cursor: pointer;
}

.upload-link:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

/* 简化的提示样式 */
.upload-tips {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 16px;
  flex-wrap: wrap;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.75rem;
  color: #64748b;
}

.tip-item .el-icon {
  font-size: 0.875rem;
  color: #3b82f6;
}

/* 紧凑型文件预览区域 */
.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-title h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.file-count-badge {
  background: #3b82f6;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.file-preview-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.file-stats-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.stats-info {
  display: flex;
  gap: 16px;
}

.stat-chip {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  color: #64748b;
}

.stat-chip .el-icon {
  color: #3b82f6;
  font-size: 0.875rem;
}

/* 紧凑型文件列表 */
.file-list-compact {
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.file-item-compact {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s ease;
}

.file-item-compact:last-child {
  border-bottom: none;
}

.file-item-compact:hover {
  background: #f8fafc;
}

.file-item-compact .file-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  border-radius: 6px;
  font-size: 1rem;
  color: #64748b;
}

.file-item-compact .file-details {
  flex: 1;
  min-width: 0;
}

.file-item-compact .file-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-item-compact .file-size {
  font-size: 0.75rem;
  color: #64748b;
}

.file-item-compact .file-actions {
  flex-shrink: 0;
}

/* 重复文件样式 */
.file-item-compact.duplicate {
  background: rgba(245, 158, 11, 0.05);
  border-left: 3px solid #f59e0b;
}

.file-item-compact.duplicate .file-name {
  color: #d97706;
}

/* 文件列表容器 */
.file-list-container {
  max-height: 300px;
  overflow-y: auto;
}

/* 文件分页样式 */
.file-pagination {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid #f1f5f9;
  background: #fafbfc;
}

.pagination-info {
  font-size: 0.75rem;
  color: #64748b;
  text-align: center;
}

.file-pagination .el-pagination {
  --el-pagination-font-size: 12px;
  --el-pagination-button-width: 28px;
  --el-pagination-button-height: 28px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-preview-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
    padding: 16px;
  }
  
  .card-header {
    height: 140px;
  }
  
  .file-stats-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .stats-info {
    justify-content: center;
  }
  
  .view-controls {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .file-preview-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 12px;
  }
  
  .card-header {
    height: 120px;
  }
  
  .file-list-item {
    padding: 12px 16px;
    gap: 12px;
  }
  
  .item-preview {
    width: 40px;
    height: 40px;
  }
  
  .progress-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
}

/* 新增的上传状态详情样式 */
.upload-status-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 16px;
}

.current-upload-info {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 20px;
}

.current-file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.current-file-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.95rem;
}

.concurrent-info {
  flex-shrink: 0;
}

.upload-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
}

.metric-item {
  text-align: center;
  padding: 12px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--border-secondary);
}

.metric-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: 4px;
  font-weight: 500;
}

.metric-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
}

.metric-value.no-data {
  color: var(--text-tertiary);
  font-style: italic;
}

.upload-queue-status {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 20px;
}

.queue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.queue-header h5 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.queue-controls {
  flex-shrink: 0;
}

.queue-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-chip {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid;
}

.stat-chip.success {
  color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.2);
}

.stat-chip.uploading {
  color: var(--primary);
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.2);
}

.stat-chip.pending {
  color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
}

.stat-chip.error {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
}

.queue-details {
  border-top: 1px solid var(--border-secondary);
  padding-top: 16px;
}

.queue-section {
  margin-bottom: 16px;
}

.queue-section:last-child {
  margin-bottom: 0;
}

.queue-section h6 {
  margin: 0 0 8px 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.file-list-mini {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.file-mini-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--bg-tertiary);
  border-radius: 6px;
  font-size: 0.8rem;
}

.file-mini-item.uploading {
  background: rgba(99, 102, 241, 0.05);
  border-left: 3px solid var(--primary);
}

.file-mini-item.pending {
  background: rgba(245, 158, 11, 0.05);
  border-left: 3px solid #f59e0b;
}

.file-mini-item .file-name {
  flex: 1;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-primary);
  font-weight: 500;
}

.file-mini-item .file-progress,
.file-mini-item .file-size {
  flex-shrink: 0;
  color: var(--text-secondary);
  font-size: 0.75rem;
}

.more-files {
  padding: 8px 12px;
  text-align: center;
  color: var(--text-tertiary);
  font-size: 0.75rem;
  font-style: italic;
  background: var(--bg-tertiary);
  border-radius: 6px;
  border: 1px dashed var(--border-secondary);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .upload-metrics {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .metric-item {
    padding: 10px;
  }
  
  .current-file-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .queue-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .stat-chip {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .upload-metrics {
    grid-template-columns: 1fr;
  }
}
</style>