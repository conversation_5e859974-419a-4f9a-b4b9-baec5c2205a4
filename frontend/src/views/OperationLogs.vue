<template>
  <div class="operation-logs-page">
    <!-- 操作日志横幅 -->
    <div class="operation-logs-banner">
      <div class="banner-background">
        <div class="file-pattern"></div>
      </div>
      <div class="banner-content">
        <div class="banner-text">
          <h1 class="banner-title">操作日志</h1>
          <p class="banner-subtitle">查看和管理系统操作记录，追踪文件变更历史</p>
          <div class="banner-actions">
            <el-button type="primary" size="large" @click="exportLogs" :loading="exporting">
              <el-icon><Download /></el-icon>
              导出日志
            </el-button>
          </div>
        </div>
        <div class="banner-visual">
          <div class="operation-logs-icon">
            <el-icon><List /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-section">
      <h2 class="section-title" style="margin-bottom: 40px !important;">
        <el-icon><DataAnalysis /></el-icon>
        操作统计
      </h2>
      <div class="stats-overview">
        <div class="stat-card primary">
          <div class="stat-icon">
            <el-icon><DataAnalysis /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ total }}</div>
            <div class="stat-label">总操作数</div>
            <div class="stat-trend">
              <el-icon><TrendCharts /></el-icon>
              <span>点击查看详情</span>
            </div>
          </div>
        </div>
        <div class="stat-card success">
          <div class="stat-icon">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ successCount }}</div>
            <div class="stat-label">成功操作</div>
            <div class="stat-trend">
              <el-icon><Check /></el-icon>
              <span>操作成功</span>
            </div>
          </div>
        </div>
        <div class="stat-card danger">
          <div class="stat-icon">
            <el-icon><CircleClose /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ failedCount }}</div>
            <div class="stat-label">失败操作</div>
            <div class="stat-trend">
              <el-icon><Close /></el-icon>
              <span>需要关注</span>
            </div>
          </div>
        </div>
        <div class="stat-card info">
          <div class="stat-icon">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ todayCount }}</div>
            <div class="stat-label">今日操作</div>
            <div class="stat-trend">
              <el-icon><Calendar /></el-icon>
              <span>24小时内</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 现代化筛选器卡片 -->
    <div class="modern-filter-card">
      <div class="filter-header">
        <div class="filter-title">
          <div class="filter-icon">
            <el-icon><Filter /></el-icon>
          </div>
          <span>筛选条件</span>
        </div>
        <el-button text type="primary" @click="resetFilters" class="reset-btn">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>
      <div class="filter-content">
        <div class="filter-grid">
          <div class="filter-group">
            <label class="filter-label">操作类型</label>
            <el-select
              v-model="filters.action"
              placeholder="全部类型"
              clearable
              class="modern-select"
            >
              <el-option label="上传文件" value="upload">
                <div class="option-content">
                  <el-icon class="option-icon upload-icon"><Upload /></el-icon>
                  <span>上传文件</span>
                </div>
              </el-option>
              <el-option label="下载文件" value="download">
                <div class="option-content">
                  <el-icon class="option-icon download-icon"><Download /></el-icon>
                  <span>下载文件</span>
                </div>
              </el-option>
              <el-option label="删除文件" value="delete">
                <div class="option-content">
                  <el-icon class="option-icon delete-icon"><Delete /></el-icon>
                  <span>删除文件</span>
                </div>
              </el-option>
              <el-option label="重命名文件" value="rename">
                <div class="option-content">
                  <el-icon class="option-icon rename-icon"><Edit /></el-icon>
                  <span>重命名文件</span>
                </div>
              </el-option>
            </el-select>
          </div>
          <div class="filter-group">
            <label class="filter-label">操作状态</label>
            <el-select
              v-model="filters.status"
              placeholder="全部状态"
              clearable
              class="modern-select"
            >
              <el-option label="成功" value="success">
                <div class="option-content">
                  <el-icon class="option-icon success-icon"><CircleCheck /></el-icon>
                  <span>成功</span>
                </div>
              </el-option>
              <el-option label="失败" value="failed">
                <div class="option-content">
                  <el-icon class="option-icon error-icon"><CircleClose /></el-icon>
                  <span>失败</span>
                </div>
              </el-option>
            </el-select>
          </div>
          <div class="filter-group">
            <label class="filter-label">时间范围</label>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="modern-date-picker"
              @change="handleDateChange"
            />
          </div>
          <div class="filter-actions">
            <el-button
              type="primary"
              @click="loadLogs"
              :loading="loading"
              class="search-btn"
            >
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 现代化日志列表卡片 -->
    <div class="modern-logs-card">
      <div class="card-header">
        <div class="card-title">
          <div class="title-icon">
            <el-icon><List /></el-icon>
          </div>
          <span>操作记录</span>
          <div class="record-count" v-if="total > 0">{{ total }} 条记录</div>
        </div>
        <div class="card-actions">
          <el-button text type="primary" @click="loadLogs" class="refresh-btn">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <div class="card-content">
        <!-- 空状态 -->
        <div v-if="!loading && logs.length === 0" class="empty-state">
          <div class="empty-illustration">
            <div class="empty-icon">
              <el-icon><DocumentRemove /></el-icon>
            </div>
            <div class="empty-content">
              <h3>暂无操作记录</h3>
              <p>当前筛选条件下没有找到操作记录</p>
              <el-button type="primary" @click="resetFilters">
                重置筛选条件
              </el-button>
            </div>
          </div>
        </div>

        <!-- 日志列表 - 时间线卡片布局 -->
        <div v-else class="logs-list">
          <div v-loading="loading" class="timeline-container">
            <div class="timeline-list">
              <div
                v-for="(log, index) in logs"
                :key="log.id"
                class="timeline-item"
                :class="{ 'timeline-item-failed': log.status !== 'success' }"
              >
                <!-- 时间线连接线 -->
                <div class="timeline-line" v-if="index < logs.length - 1"></div>
                
                <!-- 时间线节点 -->
                <div class="timeline-node" :class="getTimelineNodeClass(log)">
                  <el-icon>
                    <Upload v-if="log.action === 'upload'" />
                    <Download v-else-if="log.action === 'download'" />
                    <Delete v-else-if="log.action === 'delete'" />
                    <Edit v-else-if="log.action === 'rename'" />
                    <Operation v-else />
                  </el-icon>
                </div>
                
                <!-- 日志内容卡片 -->
                <div class="timeline-content">
                  <div class="log-card" @click="toggleLogDetail(log.id)">
                    <div class="log-header">
                      <div class="log-main-info">
                        <div class="log-action">
                          <span class="action-text">{{ getActionText(log.action) }}</span>
                          <el-tag
                            :type="log.status === 'success' ? 'success' : 'danger'"
                            size="small"
                            class="status-tag"
                          >
                            {{ log.status === 'success' ? '成功' : '失败' }}
                          </el-tag>
                        </div>
                        <div class="log-resource">
                          <el-icon class="resource-icon"><Document /></el-icon>
                          <span class="resource-path">{{ log.resource || '未知资源' }}</span>
                        </div>
                      </div>
                      <div class="log-meta">
                        <div class="log-time">
                          <el-icon><Clock /></el-icon>
                          <span>{{ formatDate(log.created_at) }}</span>
                        </div>
                        <div class="log-actions">
                          <el-button
                            size="small"
                            text
                            @click.stop="toggleLogDetail(log.id)"
                            class="detail-btn"
                          >
                            <el-icon><View /></el-icon>
                          </el-button>
                          <el-button
                            size="small"
                            type="danger"
                            text
                            @click.stop="deleteLog(log)"
                            :loading="deletingIds.includes(log.id)"
                            class="delete-btn"
                          >
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 变更详情 -->
                    <div v-if="log.old_value && log.new_value" class="log-changes">
                      <div class="change-item">
                        <span class="change-label">原名称:</span>
                        <span class="old-name">{{ log.old_value }}</span>
                      </div>
                      <div class="change-arrow">
                        <el-icon><Right /></el-icon>
                      </div>
                      <div class="change-item">
                        <span class="change-label">新名称:</span>
                        <span class="new-name">{{ log.new_value }}</span>
                      </div>
                    </div>
                    
                    <!-- 展开的详细信息 -->
                    <div v-if="expandedLogs.includes(log.id)" class="log-details">
                      <div class="detail-row">
                        <span class="detail-label">操作ID:</span>
                        <span class="detail-value">{{ log.id }}</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">完整路径:</span>
                        <span class="detail-value code">{{ log.resource || '未知' }}</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">操作时间:</span>
                        <span class="detail-value">{{ formatDate(log.created_at) }}</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">相对时间:</span>
                        <span class="detail-value">{{ getRelativeTime(log.created_at) }}</span>
                      </div>
                      <div v-if="log.status !== 'success'" class="detail-row error">
                        <span class="detail-label">错误信息:</span>
                        <span class="detail-value">{{ log.error_msg || '未知错误' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="total"
              :page-sizes="smartPageSizes"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="loadLogs"
              @current-change="loadLogs"
              background
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DocumentCopy,
  Download,
  DataAnalysis,
  CircleCheck,
  CircleClose,
  Clock,
  Filter,
  Refresh,
  Upload,
  Delete,
  Edit,
  Search,
  List,
  Document,
  Right,
  Operation,
  DocumentRemove,
  TrendCharts,
  Check,
  Close,
  Calendar,
  View
} from '@element-plus/icons-vue'
import { apiMethods } from '@/utils/api'
import type { OperationLog } from '@/types'

const logs = ref<OperationLog[]>([])
const loading = ref(false)
const exporting = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const dateRange = ref<[Date, Date]>()
const deletingIds = ref<number[]>([])
const expandedLogs = ref<number[]>([])
const showClientLogs = ref(false)

const filters = ref({
  action: '',
  status: '',
  start_date: '',
  end_date: ''
})

// 计算统计数据
const successCount = computed(() => {
  return logs.value.filter(log => log.status === 'success').length
})

const failedCount = computed(() => {
  return logs.value.filter(log => log.status !== 'success').length
})

const todayCount = computed(() => {
  const today = new Date().toDateString()
  return logs.value.filter(log => new Date(log.created_at).toDateString() === today).length
})

// 智能分页大小计算 - 基于数据量和性能的动态算法
const smartPageSizes = computed(() => {
  const totalCount = total.value
  const baseSizes = [10, 20, 50]
  
  // 动态计算合适的分页选项
  const calculateOptimalSizes = (count: number) => {
    const sizes = [...baseSizes]
    
    // 根据数据量动态添加选项
    if (count > 50) sizes.push(100)
    if (count > 200) sizes.push(200)
    if (count > 500) sizes.push(500)
    if (count > 1000) sizes.push(1000)
    
    // 对于大数据量，添加智能分段
    if (count > 2000) {
      const segments = Math.min(5, Math.floor(count / 1000)) // 最多5个分段
      for (let i = 2; i <= segments; i++) {
        sizes.push(i * 1000)
      }
    }
    
    // 添加四分之一、二分之一选项（仅当数据量较大时）
    if (count > 1000) {
      const quarter = Math.ceil(count / 4)
      const half = Math.ceil(count / 2)
      
      if (quarter > sizes[sizes.length - 1]) sizes.push(quarter)
      if (half > sizes[sizes.length - 1]) sizes.push(half)
    }
    
    // 添加全部选项（仅当数据量不超过10000时，避免性能问题）
    if (count <= 10000 && count > sizes[sizes.length - 1]) {
      sizes.push(count)
    }
    
    return sizes.sort((a, b) => a - b) // 确保排序
  }
  
  return calculateOptimalSizes(totalCount)
})

const loadLogs = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      action: filters.value.action,
      status: filters.value.status,
      start_date: filters.value.start_date,
      end_date: filters.value.end_date
    }
    
    const response = await apiMethods.logs.list(params)
    logs.value = response.data.data.logs
    total.value = response.data.data.pagination.total
    
    // 如果没有找到失败日志，尝试加载本地存储的客户端日志
    if (filters.value.status === 'failed' && logs.value.length === 0) {
      loadClientLogs()
    }
  } catch (error) {
    console.error('加载日志失败:', error)
    ElMessage.error('加载日志失败')
    // 如果服务器不可达，尝试加载本地日志
    if (filters.value.status === 'failed' || filters.value.status === '') {
      loadClientLogs()
    }
  } finally {
    loading.value = false
  }
}

// 加载本地存储的客户端日志
const loadClientLogs = () => {
  try {
    const clientLogs = JSON.parse(localStorage.getItem('clientErrorLogs') || '[]')
    console.log('[DEBUG] 本地客户端日志:', clientLogs)
    
    if (clientLogs.length > 0) {
      // 转换本地日志格式为标准格式
      const convertedLogs = clientLogs.map((log: any, index: number) => ({
        id: `client_${index}`,
        action: log.action,
        resource: log.resource,
        old_value: '',
        new_value: '',
        status: log.status,
        error_msg: log.error_msg,
        created_at: log.timestamp,
        updated_at: log.timestamp,
        user_id: 0
      }))
      
      // 应用筛选条件
      let filteredLogs = convertedLogs
      if (filters.value.action) {
        filteredLogs = filteredLogs.filter((log: OperationLog) => log.action === filters.value.action)
      }
      if (filters.value.status) {
        filteredLogs = filteredLogs.filter((log: OperationLog) => log.status === filters.value.status)
      }
      
      logs.value = filteredLogs.slice(0, pageSize.value)
      total.value = filteredLogs.length
      showClientLogs.value = true
      
      ElMessage.info(`从本地存储加载了 ${filteredLogs.length} 条客户端日志`)
    }
  } catch (error) {
    console.error('加载本地日志失败:', error)
  }
}

const handleDateChange = (dates: [Date, Date] | null) => {
  if (dates) {
    filters.value.start_date = dates[0].toISOString().split('T')[0]
    filters.value.end_date = dates[1].toISOString().split('T')[0]
  } else {
    filters.value.start_date = ''
    filters.value.end_date = ''
  }
}

const resetFilters = () => {
  filters.value = {
    action: '',
    status: '',
    start_date: '',
    end_date: ''
  }
  dateRange.value = undefined
  currentPage.value = 1
  loadLogs()
}

const getActionClass = (action: string) => {
  const classMap: Record<string, string> = {
    upload: 'action-upload',
    download: 'action-download',
    delete: 'action-delete',
    rename: 'action-rename'
  }
  return classMap[action] || 'action-default'
}

const getActionText = (action: string) => {
  const textMap: Record<string, string> = {
    upload: '上传',
    download: '下载',
    delete: '删除',
    rename: '重命名'
  }
  return textMap[action] || action
}

const deleteLog = async (log: OperationLog) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条日志记录吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    deletingIds.value.push(log.id)
    await apiMethods.logs.delete(log.id)
    ElMessage.success('日志删除成功')
    loadLogs()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除日志失败:', error)
      ElMessage.error('删除日志失败')
    }
  } finally {
    deletingIds.value = deletingIds.value.filter(id => id !== log.id)
  }
}

const exportLogs = async () => {
  try {
    exporting.value = true
    const exportData = {
      action: filters.value.action,
      status: filters.value.status,
      start_date: filters.value.start_date,
      end_date: filters.value.end_date,
      format: 'csv'
    }
    
    const response = await apiMethods.logs.export(exportData)
    
    // 创建下载链接
    const blob = new Blob([response.data], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `operation_logs_${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('日志导出成功')
  } catch (error) {
    console.error('导出日志失败:', error)
    ElMessage.error('导出日志失败')
  } finally {
    exporting.value = false
  }
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getRelativeTime = (date: string) => {
  const now = new Date()
  const logDate = new Date(date)
  const diff = now.getTime() - logDate.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  return '一周前'
}

const formatCompactDate = (date: string) => {
  const logDate = new Date(date)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const logDay = new Date(logDate.getFullYear(), logDate.getMonth(), logDate.getDate())
  
  if (logDay.getTime() === today.getTime()) {
    // 今天，只显示时间
    return logDate.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  } else {
    // 其他日期，显示月日
    return logDate.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    })
  }
}

const getTimelineNodeClass = (log: OperationLog) => {
  const baseClass = 'timeline-node'
  const actionClass = getActionClass(log.action)
  const statusClass = log.status === 'success' ? 'success' : 'failed'
  return `${baseClass} ${actionClass} ${statusClass}`
}

const toggleLogDetail = (logId: number) => {
  const index = expandedLogs.value.indexOf(logId)
  if (index > -1) {
    expandedLogs.value.splice(index, 1)
  } else {
    expandedLogs.value.push(logId)
  }
}

onMounted(() => {
  loadLogs()
})
</script>

<style scoped>
.operation-logs-page {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
  background: #fafbfc;
  min-height: 100vh;
}

/* 操作日志横幅 - 简约现代风格 */
.operation-logs-banner {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  color: #334155;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.operation-logs-banner:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

/* 现代化筛选器卡片 */
.modern-filter-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  margin-bottom: 24px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.modern-filter-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.modern-filter-card .filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f1f5f9;
}

.modern-filter-card .filter-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.filter-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 8px;
  color: white;
  font-size: 14px;
}

.reset-btn {
  color: #3b82f6 !important;
  font-weight: 500;
}

.reset-btn:hover {
  background: #eff6ff !important;
}

.filter-content {
  padding: 0 24px 24px;
}

.filter-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr auto;
  gap: 20px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin: 0;
}

.modern-select,
.modern-date-picker {
  width: 100%;
}

.modern-select :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
}

.modern-select :deep(.el-input__wrapper):hover {
  border-color: #3b82f6;
}

.modern-select :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modern-date-picker :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
}

.modern-date-picker :deep(.el-input__wrapper):hover {
  border-color: #3b82f6;
}

.modern-date-picker :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.option-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-icon {
  font-size: 14px;
}

.upload-icon { color: #10b981; }
.download-icon { color: #3b82f6; }
.delete-icon { color: #ef4444; }
.rename-icon { color: #f59e0b; }
.success-icon { color: #10b981; }
.error-icon { color: #ef4444; }

.search-btn {
  height: 40px;
  padding: 0 20px;
  border-radius: 8px;
  font-weight: 500;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  transition: all 0.2s ease;
}

.search-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

/* 现代化日志列表卡片 */
.modern-logs-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.modern-logs-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.modern-logs-card .card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #f1f5f9;
  background: #fafbfc;
}

.modern-logs-card .card-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.title-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 8px;
  color: white;
  font-size: 14px;
}

.record-count {
  background: #eff6ff;
  color: #1d4ed8;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  margin-left: 8px;
}

.refresh-btn {
  color: #3b82f6 !important;
  font-weight: 500;
}

.refresh-btn:hover {
  background: #eff6ff !important;
}

@media (max-width: 1024px) {
  .filter-grid {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
  
  .filter-actions {
    grid-column: span 2;
  }
}

@media (max-width: 768px) {
  .operation-logs-page {
    padding: 16px;
  }
  
  .filter-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .filter-actions {
    grid-column: span 1;
  }
  
  .modern-filter-card .filter-header,
  .modern-logs-card .card-header {
    padding: 16px 20px;
  }
  
  .filter-content {
    padding: 0 20px 20px;
  }
}

.banner-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.03;
}

.file-pattern {
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, #64748b 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, #64748b 1px, transparent 1px);
  background-size: 80px 80px, 120px 120px;
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.banner-text {
  flex: 1;
}

.operation-logs-banner .banner-title,
.operation-logs-page .banner-title,
h1.banner-title,
.operation-logs-banner h1,
.operation-logs-page h1,
.banner-title {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  margin: 0 0 8px 0 !important;
  color: #1e293b !important;
  letter-spacing: -0.025em !important;
  line-height: 1.2 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  text-decoration: none !important;
  outline: none !important;
  box-shadow: none !important;
  transform: none !important;
  zoom: 1 !important;
  scale: 1 !important;
}

.banner-subtitle {
  font-size: 0.95rem;
  margin: 0 0 16px 0;
  color: #64748b;
  line-height: 1.4;
  font-weight: 400;
}

.banner-actions {
  display: flex;
  gap: 12px;
}

.banner-visual {
  position: relative;
  width: 80px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.operation-logs-icon {
  width: 48px;
  height: 48px;
  background: #3b82f6;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 3px 8px rgba(59, 130, 246, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.operation-logs-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);
}

/* 仅在支持hover的设备上启用脉冲动画 */
@media (hover: hover) and (prefers-reduced-motion: no-preference) {
  .operation-logs-banner:hover .operation-logs-icon {
    animation: iconPulse 3s ease-in-out infinite;
  }
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  }
}

/* 统计概览 - 紧凑现代风格 */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 10px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 8px 10px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 48px;
  border-left: 3px solid transparent;
}

.stat-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #e5e7eb;
}

.stat-card.primary {
  border-left-color: #3b82f6;
}

.stat-card.success {
  border-left-color: #10b981;
}

.stat-card.danger {
  border-left-color: #ef4444;
}

.stat-card.info {
  border-left-color: #06b6d4;
}

.stat-card.primary:hover {
  border-left-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.stat-card.success:hover {
  border-left-color: #10b981;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.stat-card.danger:hover {
  border-left-color: #ef4444;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
}

.stat-card.info:hover {
  border-left-color: #06b6d4;
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.15);
}

.stat-icon {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  position: relative;
  z-index: 1;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.stat-card.primary .stat-icon {
  background: #3b82f6;
}

.stat-card.success .stat-icon {
  background: #10b981;
}

.stat-card.danger .stat-icon {
  background: #ef4444;
}

.stat-card.info .stat-icon {
  background: #06b6d4;
}

.stat-card:hover .stat-icon {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.stat-icon .el-icon {
  font-size: 0.875rem !important;
  line-height: 1 !important;
  width: 0.875rem !important;
  height: 0.875rem !important;
  display: inline-block !important;
}

.stat-content {
  flex: 1;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1px;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0;
  line-height: 1.1;
}

.stat-label {
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
  margin-bottom: 1px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 0.625rem;
  color: #9ca3af;
  font-weight: 400;
  opacity: 0.8;
}

.stat-trend .el-icon {
  font-size: 0.625rem;
}

/* 统计区块标题 */
.stats-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 28px 0;
  display: flex;
  align-items: center;
  gap: 10px;
  letter-spacing: -0.025em;
}

.section-title .el-icon {
  color: #3b82f6;
  font-size: 1.2rem;
}

/* 过滤器卡片 */
.filter-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  margin-bottom: 32px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.filter-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.filter-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-title .el-icon {
  color: #3b82f6;
}

.filter-content {
  padding: 24px;
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  align-items: end;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e293b;
}

.filter-actions {
  display: flex;
  align-items: end;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 日志卡片 */
.logs-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.card-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-title .el-icon {
  color: #3b82f6;
}

.card-content {
  min-height: 400px;
}

/* 空状态 */
.empty-state {
  padding: 80px 24px;
  text-align: center;
}

.empty-illustration {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-icon {
  width: 64px;
  height: 64px;
  background: #f1f5f9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #64748b;
}

.empty-content h3 {
  margin: 0 0 8px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

.empty-content p {
  margin: 0 0 24px 0;
  color: #64748b;
}

/* 时间线布局样式 */
.timeline-container {
  padding: 24px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* 自定义滚动条样式 */
.timeline-container::-webkit-scrollbar {
  width: 8px;
}

.timeline-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.timeline-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.timeline-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.timeline-list {
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.timeline-item {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-line {
  position: absolute;
  left: 16px;
  top: 32px;
  bottom: -16px;
  width: 2px;
  background: linear-gradient(to bottom, #e2e8f0 0%, #f1f5f9 100%);
  z-index: 1;
}

.timeline-node {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.875rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
  flex-shrink: 0;
  border: 2px solid white;
}

.timeline-node.action-upload.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.timeline-node.action-download.success {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.timeline-node.action-delete.success {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.timeline-node.action-rename.success {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.timeline-node.failed {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  opacity: 0.7;
}

.timeline-content {
  flex: 1;
  min-width: 0;
}

.log-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.log-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #3b82f6;
}

.timeline-item-failed .log-card {
  border-left: 3px solid #ef4444;
}

.timeline-item-failed .log-card:hover {
  border-color: #ef4444;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  margin-bottom: 0;
}

.log-main-info {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.log-action {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-text {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
}

.status-tag {
  font-weight: 500;
  border: none;
  font-size: 0.75rem;
  padding: 2px 6px;
}

.log-resource {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #64748b;
}

.resource-icon {
  color: #94a3b8;
  flex-shrink: 0;
  font-size: 0.875rem;
}

.resource-path {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.8rem;
  word-break: break-all;
  color: #475569;
}

.log-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.log-time {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 0.7rem;
  color: #94a3b8;
}

.log-actions {
  display: flex;
  gap: 2px;
}

.detail-btn,
.delete-btn {
  padding: 4px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.detail-btn:hover {
  background: #f1f5f9;
  color: #3b82f6;
}

.delete-btn:hover {
  background: #fef2f2;
  color: #ef4444;
}

.log-changes {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  margin-top: 8px;
  font-size: 0.8rem;
}

.change-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
  min-width: 0;
}

.change-label {
  font-size: 0.7rem;
  color: #94a3b8;
  font-weight: 500;
}

.old-name {
  color: #ef4444;
  text-decoration: line-through;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  word-break: break-all;
  font-size: 0.75rem;
}

.new-name {
  color: #10b981;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 500;
  word-break: break-all;
  font-size: 0.75rem;
}

.change-arrow {
  color: #94a3b8;
  flex-shrink: 0;
  margin-top: 12px;
  font-size: 0.8rem;
}

.log-details {
  border-top: 1px solid #e2e8f0;
  padding-top: 12px;
  margin-top: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 6px;
  font-size: 0.8rem;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-row.error {
  background: #fef2f2;
  padding: 6px 10px;
  border-radius: 4px;
  border-left: 3px solid #ef4444;
}

.detail-label {
  font-weight: 500;
  color: #64748b;
  flex-shrink: 0;
  min-width: 70px;
  font-size: 0.75rem;
}

.detail-value {
  color: #1e293b;
  word-break: break-all;
  text-align: right;
  font-size: 0.75rem;
}

.detail-value.code {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  background: #f1f5f9;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.7rem;
}

.detail-row.error .detail-value {
  color: #dc2626;
}

.logs-table :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: #fafbfc;
}

.logs-table :deep(.el-table--striped .el-table__body tr.el-table__row--striped:hover td) {
  background: #f1f5f9;
}

/* 表格单元格样式 */
.action-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-direction: column;
}

/* 超紧凑样式 */
.action-cell-compact {
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon-mini {
  width: 22px;
  height: 22px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.7rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.resource-cell-compact {
  display: flex;
  align-items: center;
}

.resource-name-compact {
  color: #1e293b;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.7rem;
  word-break: break-all;
}

.change-detail-compact {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.7rem;
}

.old-value-compact {
  color: #ef4444;
  text-decoration: line-through;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.65rem;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.new-value-compact {
  color: #10b981;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.65rem;
  font-weight: 500;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.change-arrow-compact {
  color: #94a3b8;
  font-size: 0.6rem;
  flex-shrink: 0;
}

.no-change-compact {
  color: #94a3b8;
  font-style: italic;
  font-size: 0.7rem;
}

.status-icon-compact {
  font-size: 0.9rem;
}

.status-success {
  color: #10b981;
}

.status-failed {
  color: #ef4444;
}

.time-cell-compact {
  display: flex;
  justify-content: center;
}

.time-compact {
  color: #1e293b;
  font-size: 0.7rem;
  font-weight: 500;
}

.delete-btn-compact {
  color: #ef4444;
  transition: all 0.2s ease;
  padding: 2px;
  border-radius: 4px;
  font-size: 0.7rem;
}

.delete-btn-compact:hover {
  background: #fef2f2;
  color: #dc2626;
}

.action-icon {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.75rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  margin-bottom: 2px;
}

.action-upload {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.action-download {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
}

.action-delete {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.action-rename {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.action-default {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.action-text {
  font-weight: 500;
  color: #1e293b;
  font-size: 0.65rem;
  text-align: center;
}

.resource-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #94a3b8;
  flex-shrink: 0;
}

.resource-name {
  color: #1e293b;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.8rem;
  word-break: break-all;
}

.change-detail {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.8rem;
}

.change-item {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.change-label {
  font-size: 0.75rem;
  color: #94a3b8;
  font-weight: 500;
  flex-shrink: 0;
}

.old-value {
  color: #ef4444;
  text-decoration: line-through;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.8rem;
  word-break: break-all;
}

.new-value {
  color: #10b981;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.8rem;
  font-weight: 500;
  word-break: break-all;
}

.change-arrow {
  color: #94a3b8;
  flex-shrink: 0;
  margin: 0 4px;
}

.no-change {
  color: #94a3b8;
  font-style: italic;
  font-size: 0.875rem;
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 3px;
  font-weight: 500;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 0.75rem;
}

.time-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
  text-align: center;
}

.time-date {
  color: #1e293b;
  font-size: 0.8rem;
  font-weight: 500;
}

.time-relative {
  color: #94a3b8;
  font-size: 0.7rem;
}

.delete-btn {
  color: #ef4444;
  transition: all 0.2s ease;
  padding: 4px;
  border-radius: 4px;
}

.delete-btn:hover {
  background: #fef2f2;
  color: #dc2626;
}

/* 分页 */
.pagination-container {
  padding: 24px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e2e8f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .operation-logs-page {
    padding: 16px;
  }
  
  .operation-logs-banner {
    padding: 24px;
    margin-bottom: 24px;
    border-radius: 12px;
  }
  
  .banner-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
  
  .banner-title {
    font-size: 1.125rem !important;
  }
  
  .banner-subtitle {
    font-size: 0.875rem;
  }
  
  .banner-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-start;
  }
  
  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .stat-card {
    padding: 10px;
    gap: 8px;
    min-height: 55px;
  }

  .stat-icon {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }

  .stat-icon .el-icon {
    font-size: 0.9rem !important;
    width: 0.9rem !important;
    height: 0.9rem !important;
  }

  .stat-value {
    font-size: 1.3rem;
  }

  .stat-label {
    font-size: 0.75rem;
  }
  
  .filter-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .table-container {
    padding: 0 16px;
  }
  
  .logs-table :deep(.el-table__body-wrapper) {
    overflow-x: auto;
  }
  
  .action-cell {
    gap: 4px;
  }
  
  .action-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }
  
  .action-text {
    font-size: 0.7rem;
  }
  
  .resource-name {
    font-size: 0.8rem;
  }
  
  .change-detail {
    flex-direction: column;
    gap: 4px;
    align-items: stretch;
  }
  
  .change-item {
    justify-content: flex-start;
  }
  
  .old-value,
  .new-value {
    font-size: 0.75rem;
  }
  
  .time-date {
    font-size: 0.8rem;
  }
  
  .time-relative {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.25rem !important;
  }

  .stats-overview {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .stat-card {
    padding: 8px;
    gap: 6px;
    min-height: 50px;
  }

  .stat-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }

  .stat-icon .el-icon {
    font-size: 0.8rem !important;
    width: 0.8rem !important;
    height: 0.8rem !important;
  }

  .stat-value {
    font-size: 1.1rem;
  }

  .stat-label {
    font-size: 0.7rem;
  }
  
  .table-container {
    padding: 0 12px;
  }
  
  .logs-table :deep(.el-table th) {
    padding: 12px 8px;
    font-size: 0.8rem;
  }
  
  .logs-table :deep(.el-table td) {
    padding: 10px 8px;
  }
  
  .action-icon {
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
  }
  
  .action-text {
    font-size: 0.65rem;
  }
  
  .resource-name {
    font-size: 0.75rem;
  }
  
  .change-detail {
    font-size: 0.75rem;
  }
  
  .old-value,
  .new-value {
    font-size: 0.7rem;
  }
  
  .time-date {
    font-size: 0.75rem;
  }
  
  .time-relative {
    font-size: 0.65rem;
  }
  
  .status-tag {
    padding: 2px 6px;
    font-size: 0.7rem;
  }
}
</style>