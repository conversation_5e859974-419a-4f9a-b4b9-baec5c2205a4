package controllers

import (
	"archive/zip"
	"crypto/sha256"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"file-manager/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type FileController struct {
	db            *gorm.DB
	logController *LogController
}

func NewFileController(db *gorm.DB, logController *LogController) *FileController {
	return &FileController{
		db:            db,
		logController: logController,
	}
}

// ListFiles 获取文件列表
func (fc *FileController) ListFiles(c *gin.Context) {
	userID := c.GetUint("user_id")
	directoryID := c.Query("directory_id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c<PERSON>("limit", "20"))
	search := c.Query("search")

	offset := (page - 1) * limit

	query := fc.db.Where("user_id = ?", userID)

	if directoryID != "" {
		if directoryID == "0" {
			query = query.Where("directory_id IS NULL")
		} else {
			query = query.Where("directory_id = ?", directoryID)
		}
	}

	if search != "" {
		query = query.Where("name LIKE ? OR original_name LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	var files []models.File
	var total int64

	query.Model(&models.File{}).Count(&total)
	query.Preload("Directory").Preload("User").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&files)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"files": files,
			"pagination": gin.H{
				"page":  page,
				"limit": limit,
				"total": total,
				"pages": (total + int64(limit) - 1) / int64(limit),
			},
		},
	})
}

// GetFile 获取单个文件信息
func (fc *FileController) GetFile(c *gin.Context) {
	userID := c.GetUint("user_id")
	fileID := c.Param("id")

	var file models.File
	if err := fc.db.Where("id = ? AND user_id = ?", fileID, userID).
		Preload("Directory").Preload("User").
		First(&file).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "File not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    file,
	})
}

// UploadFiles 上传文件
func (fc *FileController) UploadFiles(c *gin.Context) {
	userID := c.GetUint("user_id")
	directoryID := c.PostForm("directory_id")

	form, err := c.MultipartForm()
	if err != nil {
		// 记录表单解析失败日志
		fc.logController.CreateLog(userID, "upload", "multipart form", "", "", "failed", "Failed to parse multipart form: "+err.Error())
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Failed to parse multipart form",
			"error":   err.Error(),
		})
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		// 记录无文件上传日志
		fc.logController.CreateLog(userID, "upload", "no files", "", "", "failed", "No files provided")
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "No files provided",
		})
		return
	}

	var uploadedFiles []models.File
	var errors []string

	for _, fileHeader := range files {
		file, err := fc.saveUploadedFile(fileHeader, userID, directoryID)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to upload %s: %s", fileHeader.Filename, err.Error()))
			// 记录上传失败日志
			fc.logController.CreateLog(userID, "upload", fileHeader.Filename, "", "", "failed", err.Error())
			continue
		}
		uploadedFiles = append(uploadedFiles, *file)
	}

	response := models.UploadResponse{
		Success: len(uploadedFiles) > 0,
		Files:   uploadedFiles,
		Errors:  errors,
	}

	if len(uploadedFiles) == 0 {
		response.Message = "All files failed to upload"
		c.JSON(http.StatusBadRequest, response)
	} else if len(errors) > 0 {
		response.Message = fmt.Sprintf("Uploaded %d files with %d errors", len(uploadedFiles), len(errors))
		c.JSON(http.StatusPartialContent, response)
	} else {
		response.Message = fmt.Sprintf("Successfully uploaded %d files", len(uploadedFiles))
		c.JSON(http.StatusOK, response)
	}
}

// calculateFileHash 计算文件的SHA256哈希值
func (fc *FileController) calculateFileHash(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hasher := sha256.New()
	if _, err := io.Copy(hasher, file); err != nil {
		return "", err
	}

	return fmt.Sprintf("%x", hasher.Sum(nil)), nil
}

// saveUploadedFile 保存上传的文件
func (fc *FileController) saveUploadedFile(fileHeader *multipart.FileHeader, userID uint, directoryID string) (*models.File, error) {
	// 打开上传的文件
	src, err := fileHeader.Open()
	if err != nil {
		return nil, err
	}
	defer src.Close()

	// 生成唯一文件名
	ext := filepath.Ext(fileHeader.Filename)
	filename := fmt.Sprintf("%d_%d%s", userID, time.Now().UnixNano(), ext)

	// 确保上传目录存在
	uploadDir := "./uploads"
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		return nil, err
	}

	// 创建目标文件
	dst, err := os.Create(filepath.Join(uploadDir, filename))
	if err != nil {
		return nil, err
	}
	defer dst.Close()

	// 复制文件内容
	if _, err := io.Copy(dst, src); err != nil {
		return nil, err
	}

	// 获取文件信息
	fileInfo, err := dst.Stat()
	if err != nil {
		return nil, err
	}

	// 计算文件hash
	filePath := filepath.Join(uploadDir, filename)
	fileHash, err := fc.calculateFileHash(filePath)
	if err != nil {
		os.Remove(filePath)
		return nil, fmt.Errorf("计算文件hash失败: %v", err)
	}

	// 基于hash进行去重检查（更精确的去重方式）
	var existingFile models.File
	hashQuery := fc.db.Where("user_id = ? AND hash = ?", userID, fileHash)

	// 添加调试信息
	fmt.Printf("DEBUG: 检查重复文件 - 用户ID: %d, 文件名: %s, 大小: %d, Hash: %s, 目录ID: %s\n",
		userID, fileHeader.Filename, fileInfo.Size(), fileHash, directoryID)

	if err := hashQuery.First(&existingFile).Error; err == nil {
		// 基于hash发现重复文件，删除刚上传的文件并返回错误信息
		fmt.Printf("DEBUG: 发现重复文件(基于hash) - ID: %d, 文件名: %s, Hash: %s\n",
			existingFile.ID, existingFile.OriginalName, existingFile.Hash)
		os.Remove(filePath)
		return nil, fmt.Errorf("文件内容重复，已存在相同内容的文件 '%s'", existingFile.OriginalName)
	}

	// 如果hash不重复，再检查同目录下的文件名+大小组合（兼容性检查）
	nameQuery := fc.db.Where("user_id = ? AND original_name = ? AND size = ?", userID, fileHeader.Filename, fileInfo.Size())

	// 如果指定了目录，也要匹配目录
	if directoryID != "" && directoryID != "0" {
		dirID, _ := strconv.ParseUint(directoryID, 10, 32)
		dirIDUint := uint(dirID)
		nameQuery = nameQuery.Where("directory_id = ?", dirIDUint)
	} else {
		nameQuery = nameQuery.Where("directory_id IS NULL")
	}

	if err := nameQuery.First(&existingFile).Error; err == nil {
		// 同名同大小文件已存在，删除刚上传的文件并返回错误信息
		fmt.Printf("DEBUG: 发现重复文件(基于名称+大小) - ID: %d, 文件名: %s, 大小: %d\n",
			existingFile.ID, existingFile.OriginalName, existingFile.Size)
		os.Remove(filePath)
		return nil, fmt.Errorf("文件 '%s' 已存在，请勿重复上传", fileHeader.Filename)
	}

	fmt.Printf("DEBUG: 未发现重复文件，继续上传\n")

	// 创建数据库记录
	file := models.File{
		Name:         filename,
		OriginalName: fileHeader.Filename,
		Path:         filePath,
		Size:         fileInfo.Size(),
		Hash:         fileHash,
		MimeType:     fileHeader.Header.Get("Content-Type"),
		Extension:    strings.TrimPrefix(ext, "."),
		UserID:       userID,
	}

	if directoryID != "" && directoryID != "0" {
		dirID, _ := strconv.ParseUint(directoryID, 10, 32)
		dirIDUint := uint(dirID)
		file.DirectoryID = &dirIDUint
	}

	if err := fc.db.Create(&file).Error; err != nil {
		// 删除已上传的文件
		os.Remove(file.Path)
		return nil, err
	}

	// 记录上传日志
	fc.logController.CreateLog(userID, "upload", file.OriginalName, "", file.OriginalName, "success", "")

	return &file, nil
}

// DownloadFile 下载文件
func (fc *FileController) DownloadFile(c *gin.Context) {
	userID := c.GetUint("user_id")
	fileID := c.Param("id")

	var file models.File
	if err := fc.db.Where("id = ? AND user_id = ?", fileID, userID).First(&file).Error; err != nil {
		// 记录下载失败日志（文件未找到）
		fc.logController.CreateLog(userID, "download", fmt.Sprintf("File ID: %s", fileID), "", "", "failed", "File not found")
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "File not found",
		})
		return
	}

	// 检查文件是否存在
	if _, err := os.Stat(file.Path); os.IsNotExist(err) {
		// 记录下载失败日志（物理文件不存在）
		fc.logController.CreateLog(userID, "download", file.OriginalName, "", "", "failed", "File not found on disk")
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "File not found on disk",
		})
		return
	}

	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", "attachment; filename="+file.OriginalName)
	c.Header("Content-Type", "application/octet-stream")
	c.File(file.Path)

	// 记录下载日志
	fc.logController.CreateLog(userID, "download", file.OriginalName, "", file.OriginalName, "success", "")
}

// DeleteFile 删除文件
func (fc *FileController) DeleteFile(c *gin.Context) {
	userID := c.GetUint("user_id")
	fileID := c.Param("id")

	var file models.File
	if err := fc.db.Where("id = ? AND user_id = ?", fileID, userID).First(&file).Error; err != nil {
		// 记录删除失败日志（文件未找到）
		fc.logController.CreateLog(userID, "delete", fmt.Sprintf("File ID: %s", fileID), "", "", "failed", "File not found")
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "File not found",
		})
		return
	}

	// 删除物理文件
	if err := os.Remove(file.Path); err != nil && !os.IsNotExist(err) {
		// 记录删除失败日志（物理文件删除失败）
		fc.logController.CreateLog(userID, "delete", file.OriginalName, file.OriginalName, "", "failed", err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to delete file from disk",
			"error":   err.Error(),
		})
		return
	}

	// 删除数据库记录
	if err := fc.db.Delete(&file).Error; err != nil {
		// 记录删除失败日志（数据库记录删除失败）
		fc.logController.CreateLog(userID, "delete", file.OriginalName, file.OriginalName, "", "failed", err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to delete file record",
			"error":   err.Error(),
		})
		return
	}

	// 记录删除日志
	fc.logController.CreateLog(userID, "delete", file.OriginalName, file.OriginalName, "", "success", "")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "File deleted successfully",
	})
}

// UpdateFile 更新文件信息
func (fc *FileController) UpdateFile(c *gin.Context) {
	userID := c.GetUint("user_id")
	fileID := c.Param("id")

	var req struct {
		Name        string `json:"name"`
		NewName     string `json:"new_name"`
		DirectoryID *uint  `json:"directory_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	var file models.File
	if err := fc.db.Where("id = ? AND user_id = ?", fileID, userID).First(&file).Error; err != nil {
		// 记录重命名失败日志（文件未找到）
		fc.logController.CreateLog(userID, "rename", fmt.Sprintf("File ID: %s", fileID), "", req.NewName, "failed", "File not found")
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "File not found",
		})
		return
	}

	// 记录原始值用于日志
	oldName := file.OriginalName

	// 支持重命名功能
	if req.NewName != "" {
		file.OriginalName = req.NewName
	}
	if req.Name != "" {
		file.Name = req.Name
	}
	if req.DirectoryID != nil {
		file.DirectoryID = req.DirectoryID
	}

	if err := fc.db.Save(&file).Error; err != nil {
		// 记录重命名失败日志（数据库更新失败）
		if req.NewName != "" && oldName != req.NewName {
			fc.logController.CreateLog(userID, "rename", file.Path, oldName, req.NewName, "failed", err.Error())
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to update file",
			"error":   err.Error(),
		})
		return
	}

	// 记录重命名日志（如果文件名发生了变化）
	if req.NewName != "" && oldName != req.NewName {
		fc.logController.CreateLog(userID, "rename", file.Path, oldName, req.NewName, "success", "")
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "File updated successfully",
		"data":    file,
	})
}

// SearchFiles 搜索文件
func (fc *FileController) SearchFiles(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req struct {
		Query     string `json:"query"`
		Extension string `json:"extension"`
		MinSize   int64  `json:"min_size"`
		MaxSize   int64  `json:"max_size"`
		StartDate string `json:"start_date"`
		EndDate   string `json:"end_date"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	query := fc.db.Where("user_id = ?", userID)

	if req.Query != "" {
		query = query.Where("name LIKE ? OR original_name LIKE ?", "%"+req.Query+"%", "%"+req.Query+"%")
	}

	if req.Extension != "" {
		query = query.Where("extension = ?", req.Extension)
	}

	if req.MinSize > 0 {
		query = query.Where("size >= ?", req.MinSize)
	}

	if req.MaxSize > 0 {
		query = query.Where("size <= ?", req.MaxSize)
	}

	if req.StartDate != "" {
		query = query.Where("created_at >= ?", req.StartDate)
	}

	if req.EndDate != "" {
		query = query.Where("created_at <= ?", req.EndDate)
	}

	var files []models.File
	query.Preload("Directory").Preload("User").
		Order("created_at DESC").
		Find(&files)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    files,
	})
}

// BatchSearchFiles 批量搜索文件
func (fc *FileController) BatchSearchFiles(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req struct {
		Keywords []string `json:"keywords" binding:"required"`
		Page     int      `json:"page"`
		Limit    int      `json:"limit"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	// 验证关键词数量
	if len(req.Keywords) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "No keywords provided",
		})
		return
	}

	if len(req.Keywords) > 100 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Too many keywords, maximum 100 allowed",
		})
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 20
	}

	offset := (req.Page - 1) * req.Limit

	query := fc.db.Where("user_id = ?", userID)

	// 构建批量搜索条件 - 搜索包含任意关键词的文件
	if len(req.Keywords) > 0 {
		var conditions []string
		var args []interface{}

		for _, keyword := range req.Keywords {
			if keyword != "" {
				conditions = append(conditions, "(name LIKE ? OR original_name LIKE ?)")
				args = append(args, "%"+keyword+"%", "%"+keyword+"%")
			}
		}

		if len(conditions) > 0 {
			whereClause := strings.Join(conditions, " OR ")
			query = query.Where(whereClause, args...)
		}
	}

	var files []models.File
	var total int64

	// 获取总数
	query.Model(&models.File{}).Count(&total)

	// 获取文件列表
	query.Preload("Directory").Preload("User").
		Offset(offset).Limit(req.Limit).
		Order("created_at DESC").
		Find(&files)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"files": files,
			"pagination": gin.H{
				"page":  req.Page,
				"limit": req.Limit,
				"total": total,
				"pages": (total + int64(req.Limit) - 1) / int64(req.Limit),
			},
		},
	})
}

// ListDirectories 获取目录列表
func (fc *FileController) ListDirectories(c *gin.Context) {
	userID := c.GetUint("user_id")
	parentID := c.Query("parent_id")

	query := fc.db.Where("user_id = ?", userID)

	if parentID != "" {
		if parentID == "0" {
			query = query.Where("parent_id IS NULL")
		} else {
			query = query.Where("parent_id = ?", parentID)
		}
	}

	var directories []models.Directory
	query.Preload("Parent").Preload("User").
		Order("name ASC").
		Find(&directories)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    directories,
	})
}

// CreateDirectory 创建目录
func (fc *FileController) CreateDirectory(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req struct {
		Name     string `json:"name" binding:"required"`
		ParentID *uint  `json:"parent_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	// 构建路径
	path := req.Name
	if req.ParentID != nil {
		var parent models.Directory
		if err := fc.db.First(&parent, *req.ParentID).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "Parent directory not found",
			})
			return
		}
		path = filepath.Join(parent.Path, req.Name)
	}

	directory := models.Directory{
		Name:     req.Name,
		Path:     path,
		ParentID: req.ParentID,
		UserID:   userID,
	}

	if err := fc.db.Create(&directory).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to create directory",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Directory created successfully",
		"data":    directory,
	})
}

// GetDirectory 获取目录信息
func (fc *FileController) GetDirectory(c *gin.Context) {
	userID := c.GetUint("user_id")
	directoryID := c.Param("id")

	var directory models.Directory
	if err := fc.db.Where("id = ? AND user_id = ?", directoryID, userID).
		Preload("Parent").Preload("User").
		First(&directory).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Directory not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    directory,
	})
}

// UpdateDirectory 更新目录
func (fc *FileController) UpdateDirectory(c *gin.Context) {
	userID := c.GetUint("user_id")
	directoryID := c.Param("id")

	var req struct {
		Name string `json:"name" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	var directory models.Directory
	if err := fc.db.Where("id = ? AND user_id = ?", directoryID, userID).First(&directory).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Directory not found",
		})
		return
	}

	directory.Name = req.Name
	// 更新路径
	if directory.ParentID != nil {
		var parent models.Directory
		if err := fc.db.First(&parent, *directory.ParentID).Error; err == nil {
			directory.Path = filepath.Join(parent.Path, req.Name)
		}
	} else {
		directory.Path = req.Name
	}

	if err := fc.db.Save(&directory).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to update directory",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Directory updated successfully",
		"data":    directory,
	})
}

// DeleteDirectory 删除目录
func (fc *FileController) DeleteDirectory(c *gin.Context) {
	userID := c.GetUint("user_id")
	directoryID := c.Param("id")

	var directory models.Directory
	if err := fc.db.Where("id = ? AND user_id = ?", directoryID, userID).First(&directory).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Directory not found",
		})
		return
	}

	// 检查是否有子目录或文件
	var childCount int64
	fc.db.Model(&models.Directory{}).Where("parent_id = ?", directoryID).Count(&childCount)

	var fileCount int64
	fc.db.Model(&models.File{}).Where("directory_id = ?", directoryID).Count(&fileCount)

	if childCount > 0 || fileCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Directory is not empty",
		})
		return
	}

	if err := fc.db.Delete(&directory).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to delete directory",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Directory deleted successfully",
	})
}

// GetDirectoryFiles 获取目录下的文件
func (fc *FileController) GetDirectoryFiles(c *gin.Context) {
	userID := c.GetUint("user_id")
	directoryID := c.Param("id")

	var files []models.File
	fc.db.Where("directory_id = ? AND user_id = ?", directoryID, userID).
		Preload("Directory").Preload("User").
		Order("name ASC").
		Find(&files)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    files,
	})
}

// GetSystemInfo 获取系统信息
func (fc *FileController) GetSystemInfo(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"version":    "1.0.0",
			"go_version": "1.21",
			"os":         "linux",
			"arch":       "amd64",
		},
	})
}

// GetStats 获取统计信息
func (fc *FileController) GetStats(c *gin.Context) {
	userID := c.GetUint("user_id")

	var fileCount int64
	var totalSize int64
	var directoryCount int64

	fc.db.Model(&models.File{}).Where("user_id = ?", userID).Count(&fileCount)
	fc.db.Model(&models.File{}).Where("user_id = ?", userID).Select("COALESCE(SUM(size), 0)").Scan(&totalSize)
	fc.db.Model(&models.Directory{}).Where("user_id = ?", userID).Count(&directoryCount)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"file_count":      fileCount,
			"directory_count": directoryCount,
			"total_size":      totalSize,
		},
	})
}

// DownloadFilesAsZip 批量下载文件为ZIP包
func (fc *FileController) DownloadFilesAsZip(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req struct {
		FileIDs   []uint          `json:"file_ids" binding:"required"`
		FileNames map[uint]string `json:"file_names,omitempty"` // 可选的重命名映射：文件ID -> 新文件名
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	// 添加调试日志
	fmt.Printf("DEBUG: DownloadFilesAsZip - FileIDs: %v, FileNames: %v\n", req.FileIDs, req.FileNames)

	if len(req.FileIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "No files specified",
		})
		return
	}

	// 获取文件信息
	var files []models.File
	if err := fc.db.Where("id IN ? AND user_id = ?", req.FileIDs, userID).Find(&files).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to fetch files",
			"error":   err.Error(),
		})
		return
	}

	if len(files) == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "No files found",
		})
		return
	}

	// 设置响应头
	zipFilename := fmt.Sprintf("files_%d_%d.zip", userID, time.Now().Unix())
	c.Header("Content-Type", "application/zip")
	c.Header("Content-Disposition", "attachment; filename="+zipFilename)

	// 创建ZIP写入器
	zipWriter := zip.NewWriter(c.Writer)
	defer zipWriter.Close()

	// 添加文件到ZIP
	for _, file := range files {
		// 检查文件是否存在
		if _, err := os.Stat(file.Path); os.IsNotExist(err) {
			continue // 跳过不存在的文件
		}

		// 打开源文件
		srcFile, err := os.Open(file.Path)
		if err != nil {
			continue // 跳过无法打开的文件
		}

		// 确定在ZIP中使用的文件名
		var zipFileName string
		if req.FileNames != nil && req.FileNames[file.ID] != "" {
			// 使用重命名后的文件名
			zipFileName = req.FileNames[file.ID]
			fmt.Printf("DEBUG: Using renamed file: %d -> %s\n", file.ID, zipFileName)
		} else {
			// 使用原始文件名
			zipFileName = file.OriginalName
			fmt.Printf("DEBUG: Using original file: %d -> %s\n", file.ID, zipFileName)
		}

		// 在ZIP中创建文件
		zipFile, err := zipWriter.Create(zipFileName)
		if err != nil {
			srcFile.Close()
			continue
		}

		// 复制文件内容
		_, err = io.Copy(zipFile, srcFile)
		srcFile.Close()
		if err != nil {
			continue
		}
	}
}
