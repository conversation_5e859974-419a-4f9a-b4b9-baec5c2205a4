package routes

import (
	"file-manager/controllers"
	"file-manager/middleware"
	"file-manager/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func SetupRoutes(r *gin.Engine, db *gorm.DB) {
	// 初始化控制器
	authController := controllers.NewAuthController(db)
	logController := controllers.NewLogController(db)
	fileController := controllers.NewFileController(db, logController)
	renameController := controllers.NewRenameController(db)

	// API版本分组
	v1 := r.Group("/api/v1")
	{
		// 公开路由（不需要认证）
		auth := v1.Group("/auth")
		{
			auth.POST("/register", authController.Register)
			auth.POST("/login", authController.Login)
			auth.POST("/refresh", authController.RefreshToken)
		}

		// 需要认证的路由
		protected := v1.Group("/")
		protected.Use(middleware.AuthMiddleware(utils.GetJWTSecret()))
		{
			// 用户相关
			users := protected.Group("/users")
			{
				users.GET("/profile", authController.GetProfile)
				users.PUT("/profile", authController.UpdateProfile)
				users.POST("/change-password", authController.ChangePassword)
			}

			// 文件管理相关
			files := protected.Group("/files")
			{
				files.GET("/", fileController.ListFiles)
				files.GET("/:id", fileController.GetFile)
				files.POST("/upload", fileController.UploadFiles)
				files.GET("/:id/download", fileController.DownloadFile)
				files.POST("/download-zip", fileController.DownloadFilesAsZip)
				files.DELETE("/:id", fileController.DeleteFile)
				files.PUT("/:id", fileController.UpdateFile)
				files.POST("/search", fileController.SearchFiles)
				files.POST("/batch-search", fileController.BatchSearchFiles)
			}

			// 目录管理相关
			directories := protected.Group("/directories")
			{
				directories.GET("/", fileController.ListDirectories)
				directories.POST("/", fileController.CreateDirectory)
				directories.GET("/:id", fileController.GetDirectory)
				directories.PUT("/:id", fileController.UpdateDirectory)
				directories.DELETE("/:id", fileController.DeleteDirectory)
				directories.GET("/:id/files", fileController.GetDirectoryFiles)
			}

			// 批量重命名相关
			rename := protected.Group("/rename")
			{
				rename.POST("/preview", renameController.PreviewRename)
				rename.POST("/download", renameController.DownloadRenamedFiles)
				rename.GET("/operations", renameController.GetRenameOperations)
				rename.POST("/operations", renameController.SaveRenameOperation)
				rename.DELETE("/operations/:id", renameController.DeleteRenameOperation)
			}

			// 操作日志相关
			logs := protected.Group("/logs")
			{
				logs.GET("/", logController.GetLogs)
				logs.GET("/:id", logController.GetLog)
				logs.POST("/", logController.CreateLogAPI)
				logs.DELETE("/:id", logController.DeleteLog)
				logs.POST("/export", logController.ExportLogs)
				logs.GET("/stats", logController.GetLogStats)
				logs.POST("/cleanup", logController.CleanupLogs)
			}

			// 系统信息相关
			system := protected.Group("/system")
			{
				system.GET("/info", fileController.GetSystemInfo)
				system.GET("/stats", fileController.GetStats)
			}
		}
	}

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "File Manager API is running",
		})
	})

	// 静态文件服务
	r.Static("/uploads", "./uploads")
}
